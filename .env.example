# Backend (Strapi) Configuration
HOST=0.0.0.0
PORT=1337
APP_KEYS=1HwdEoqcHeMAHHOKd2GT3w==,cJgut5x+p9pLReHVCohb6Q==,5GeGETVsDi2mHRfFwFTVtA==,gD9aqP2m077UrO1AsXb6zQ==
API_TOKEN_SALT=UWQBFkVprokBKe4HmwPfwQ==
ADMIN_JWT_SECRET=wsby4XurQaMjNqKUzAU+Og==
TRANSFER_TOKEN_SALT=ksAOnzkZB9foRgzlBtyFEA==
JWT_SECRET=KA8LxmxSi/vwrVyIoM9ZJg==

# Database
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db

# Frontend Configuration
# IMPORTANT: This must be included in GitLab CI ENV_FILE_CONTENT variable
# For Docker Compose: use service name
STRAPI_API_URL=http://backend-eguard:1337

# For local development: use localhost
# STRAPI_API_URL=http://localhost:1337

# Environment
ENV_FILE=.env
TAG=development
