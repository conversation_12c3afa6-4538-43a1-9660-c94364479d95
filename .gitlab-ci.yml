variables:
  IMAGE_NAME_FRONTEND: dimastw/fe-eguard-web
  IMAGE_NAME_BACKEND: dimastw/be-eguard-web
  DOCKER_DRIVER: overlay2
  DEFAULT_TAG: "latest" # Default tag if CI_COMMIT_BRANCH is not set

stages:
  - build
  - deploy

frontend-build:
  stage: build
  tags:
    - docker
  image: docker:24.0.5
  services:
    - name: docker:24.0.5-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
    DOCKER_BUILDKIT: 1
  before_script:
    # Wait for Docker daemon and TLS certs to be ready
    - sleep 10
    - until docker info; do echo "Waiting for Docker daemon..."; sleep 2; done
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
  script:
    - cd frontend
    # Enable BuildKit for faster builds and better caching
    - docker build
      --pull
      --cache-from "${IMAGE_NAME_FRONTEND}:latest"
      --build-arg BUILDKIT_INLINE_CACHE=1
      -t "${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
      -t "${IMAGE_NAME_FRONTEND}:latest"
      .
    - docker push "${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
    - docker push "${IMAGE_NAME_FRONTEND}:latest"
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

backend-build:
  stage: build
  tags:
    - docker
  image: docker:24.0.5
  services:
    - name: docker:24.0.5-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
    DOCKER_BUILDKIT: 1
  before_script:
    # Wait for Docker daemon and TLS certs to be ready
    - sleep 10
    - until docker info; do echo "Waiting for Docker daemon..."; sleep 2; done
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
  script:
    - cd backend
    # Enable BuildKit for faster builds and better caching
    - docker build
      --pull
      --cache-from "${IMAGE_NAME_BACKEND}:latest"
      --build-arg BUILDKIT_INLINE_CACHE=1
      -t "${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
      -t "${IMAGE_NAME_BACKEND}:latest"
      .
    - docker push "${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
    - docker push "${IMAGE_NAME_BACKEND}:latest"
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

frontend-deploy:
  stage: deploy
  variables:
    TAG: "${CI_COMMIT_BRANCH:-$DEFAULT_TAG}" # Pass the tag to docker-compose
  tags:
    - deploy
  needs:
    - job: frontend-build
    - job: backend-build
    - job: backend-deploy
  before_script:
    - sudo rm -rf backend/.tmp || true # Prevent permission issues
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
  script:
    # Clean up old containers and images
    - docker compose down frontend || true
    - docker stop $(docker ps -q --filter "ancestor=${IMAGE_NAME_FRONTEND}") || true
    - docker container prune -f || true
    - docker image prune -f || true
    # Pull latest image and deploy
    - docker pull ${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}
    - docker compose up -d frontend
    # Verify deployment
    - docker compose ps frontend
  rules:
    - if: $CI_COMMIT_BRANCH == 'development'

backend-deploy:
  stage: deploy
  variables:
    TAG: "${CI_COMMIT_BRANCH:-$DEFAULT_TAG}" # Pass the tag to docker-compose
  tags:
    - deploy
  needs:
    - job: backend-build
  before_script:
    - sudo rm -rf backend/.tmp || true # Prevent permission issues
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    - echo "$ENV_FILE_CONTENT" > .env # Write the file variable content to .env
  script:
    # Clean up old containers and images
    - docker compose down backend-eguard || true
    - docker stop $(docker ps -q --filter "ancestor=${IMAGE_NAME_BACKEND}") || true
    - docker container prune -f || true
    - docker image prune -f || true
    # Pull latest image and deploy
    - docker pull ${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}
    - docker compose up -d backend-eguard
    # Verify deployment
    - docker compose ps backend-eguard
  rules:
    - if: $CI_COMMIT_BRANCH == 'development'
