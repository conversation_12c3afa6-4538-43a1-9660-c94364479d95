variables:
  IMAGE_NAME_FRONTEND: dimastw/fe-eguard-web
  IMAGE_NAME_BACKEND: dimastw/be-eguard-web
  DOCKER_DRIVER: overlay2
  DEFAULT_TAG: "latest" # Default tag if CI_COMMIT_BRANCH is not set

stages:
  - build
  - deploy

frontend-build:
  stage: build
  tags:
    - docker
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  cache:
    key: "frontend-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - frontend-cache/
  script:
    - mkdir -p "${CI_PROJECT_DIR}/frontend-cache"
    - echo "{\"auths\":{\"https://index.docker.io/v1/\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}/frontend"
      --dockerfile "${CI_PROJECT_DIR}/frontend/Dockerfile"
      --destination "${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
      --cache=true
      --cache-dir="${CI_PROJECT_DIR}/frontend-cache"
      --cache-ttl=168h
      --no-push-cache
      --use-new-run
      --snapshot-mode=redo
      --ignore-path=/tmp
      --ignore-path=/var/run
      --ignore-path=/var/log
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

backend-build:
  stage: build
  tags:
    - docker
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  cache:
    key: "backend-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - backend-cache/
  script:
    - mkdir -p "${CI_PROJECT_DIR}/backend-cache"
    - echo "{\"auths\":{\"https://index.docker.io/v1/\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}/backend"
      --dockerfile "${CI_PROJECT_DIR}/backend/Dockerfile"
      --destination "${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
      --cache=true
      --cache-dir="${CI_PROJECT_DIR}/backend-cache"
      --cache-ttl=168h
      --no-push-cache
      --use-new-run
      --snapshot-mode=redo
      --ignore-path=/tmp
      --ignore-path=/var/run
      --ignore-path=/var/log
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

frontend-deploy:
  stage: deploy
  variables:
    TAG: "${CI_COMMIT_BRANCH:-$DEFAULT_TAG}" # Pass the tag to docker-compose
  tags:
    - deploy
  needs:
    - job: frontend-build
    - job: backend-build
    - job: backend-deploy
  before_script:
    - sudo rm -rf backend/.tmp || true # Prevent permission issues
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
  script:
    # Clean up old containers and images
    - docker compose down frontend || true
    - docker stop $(docker ps -q --filter "ancestor=${IMAGE_NAME_FRONTEND}") || true
    - docker container prune -f || true
    - docker image prune -f || true
    # Pull latest image and deploy
    - docker pull ${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}
    - docker compose up -d frontend
    # Verify deployment
    - docker compose ps frontend
  rules:
    - if: $CI_COMMIT_BRANCH == 'development'

backend-deploy:
  stage: deploy
  variables:
    TAG: "${CI_COMMIT_BRANCH:-$DEFAULT_TAG}" # Pass the tag to docker-compose
  tags:
    - deploy
  needs:
    - job: backend-build
  before_script:
    - sudo rm -rf backend/.tmp || true # Prevent permission issues
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    - echo "$ENV_FILE_CONTENT" > .env # Write the file variable content to .env
  script:
    # Clean up old containers and images
    - docker compose down backend-eguard || true
    - docker stop $(docker ps -q --filter "ancestor=${IMAGE_NAME_BACKEND}") || true
    - docker container prune -f || true
    - docker image prune -f || true
    # Pull latest image and deploy
    - docker pull ${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}
    - docker compose up -d backend-eguard
    # Verify deployment
    - docker compose ps backend-eguard
  rules:
    - if: $CI_COMMIT_BRANCH == 'development'
