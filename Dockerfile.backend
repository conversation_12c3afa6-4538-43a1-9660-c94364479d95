# Build stage
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package.json and yarn.lock from the context root
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile --production --ignore-scripts

# Copy the rest of the application code from the context root
COPY . ./

# Build Strapi admin panel
RUN yarn build

# Runtime stage
FROM node:22-alpine

WORKDIR /app

# Copy only necessary files from builder
COPY --from=builder /app/package.json /app/yarn.lock ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist  # Adjust if build output differs

# Expose Strapi port
EXPOSE 1337

# Start Strapi
CMD ["yarn", "start"]