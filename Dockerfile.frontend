FROM node:20-bullseye-slim as base
ENV NODE_ENV=development
WORKDIR /myapp

FROM base as deps
# Copy package files for npm install
COPY frontend/package.json frontend/package-lock.json* ./
RUN npm install

FROM base as build
ENV NODE_ENV=development
COPY --from=deps /myapp/node_modules /myapp/node_modules
COPY frontend/. .
RUN npm install 
RUN npm run build

FROM base as deploy
ENV PORT="8080"
# Copy runtime dependencies
COPY --from=deps /myapp/node_modules /myapp/node_modules
# Copy build artifacts
COPY --from=build /myapp/build /myapp/build
COPY --from=build /myapp/public /myapp/public
# Copy package files
COPY frontend/package.json frontend/package-lock.json* ./
# Copy and setup start script
COPY frontend/start.sh ./
RUN chmod +x start.sh

EXPOSE 8080
CMD ["./start.sh"]