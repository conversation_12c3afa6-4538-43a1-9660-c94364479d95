# Node modules (will be installed in container)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
*.md

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Build artifacts that shouldn't be in source (but allow them for Docker builds)
# dist/ - commented out to allow copying from builder stage
# build/ - commented out to allow copying from builder stage
.tmp/

# Strapi specific
.strapi-updater.json
.cache/
public/uploads/*
!public/uploads/.gitkeep

# Database
*.sqlite
*.sqlite3
*.db

# Testing
.nyc_output/
test/

# Misc
.eslintcache
