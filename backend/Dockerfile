# Dependencies stage - install all dependencies
FROM node:20-alpine AS deps
WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile --production=false --network-timeout 100000 --prefer-offline

# Build stage - build the application
FROM node:20-alpine AS builder
WORKDIR /app
# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY package.json yarn.lock ./
# Copy source code (excluding files in .dockerignore)
COPY . ./
# Set NODE_ENV for build
ENV NODE_ENV=production
# Build Strapi admin panel
RUN yarn build

# Production dependencies stage
FROM node:20-alpine AS prod-deps
WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile --production=true --network-timeout 100000 --prefer-offline && \
    yarn cache clean

# Production stage
FROM node:20-alpine AS production
# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S strapi -u 1001

WORKDIR /app

# Copy package files
COPY package.json yarn.lock ./

# Copy production dependencies from prod-deps stage
COPY --from=prod-deps /app/node_modules ./node_modules

# Copy application files from builder stage
COPY --from=builder --chown=strapi:nodejs /app/build ./build
COPY --from=builder --chown=strapi:nodejs /app/config ./config
COPY --from=builder --chown=strapi:nodejs /app/database ./database
COPY --from=builder --chown=strapi:nodejs /app/src ./src
COPY --from=builder --chown=strapi:nodejs /app/public ./public
COPY --from=builder --chown=strapi:nodejs /app/favicon.png ./

# Create necessary directories with proper permissions
RUN mkdir -p .tmp public/uploads && \
    chown -R strapi:nodejs /app && \
    chmod -R 755 /app

# Set production environment variables
ENV NODE_ENV=production
ENV STRAPI_TELEMETRY_DISABLED=true

# Switch to non-root user
USER strapi

# Expose Strapi port
EXPOSE 1337

# Start Strapi directly
CMD ["yarn", "start"]