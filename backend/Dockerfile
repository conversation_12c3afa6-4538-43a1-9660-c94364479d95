# Dependencies stage - install all dependencies
FROM node:20-slim AS deps
WORKDIR /app

COPY package.json yarn.lock* package-lock.json* ./

# Use npm for better native module support (Sharp, better-sqlite3, etc.)
RUN npm ci

# Build stage - build the application
FROM node:20-slim AS builder
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY package.json yarn.lock* package-lock.json* ./

# Copy source code (excluding files in .dockerignore)
COPY . ./

# Set NODE_ENV for build
ENV NODE_ENV=production

# Build Strapi admin panel
RUN npm run build

# Production dependencies stage
FROM node:20-slim AS prod-deps
WORKDIR /app

COPY package.json yarn.lock* package-lock.json* ./

# Install production dependencies with npm (better native module support)
RUN npm ci --omit=dev

# Production stage
FROM node:20-slim AS production

# Create app user for security
RUN groupadd --gid 1001 nodejs && \
    useradd --uid 1001 --gid nodejs --shell /bin/bash --create-home strapi

WORKDIR /app

# Copy package files
COPY package.json yarn.lock* package-lock.json* ./

# Copy production dependencies from prod-deps stage
COPY --from=prod-deps /app/node_modules ./node_modules

# Copy application files from builder stage
COPY --from=builder --chown=strapi:nodejs /app/build ./build
COPY --from=builder --chown=strapi:nodejs /app/config ./config
COPY --from=builder --chown=strapi:nodejs /app/database ./database
COPY --from=builder --chown=strapi:nodejs /app/src ./src
COPY --from=builder --chown=strapi:nodejs /app/public ./public
COPY --from=builder --chown=strapi:nodejs /app/favicon.png ./

# Create necessary directories with proper permissions
RUN mkdir -p .tmp public/uploads && \
    chown -R strapi:nodejs /app && \
    chmod -R 755 /app

# Set production environment variables
ENV NODE_ENV=production
ENV STRAPI_TELEMETRY_DISABLED=true

# Switch to non-root user
USER strapi

# Expose Strapi port
EXPOSE 1337

# Start Strapi directly
CMD ["npm", "start"]