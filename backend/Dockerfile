# Dependencies stage - install all dependencies
FROM node:20-slim AS deps
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

COPY package.json yarn.lock* pnpm-lock.yaml* ./

# Generate pnpm-lock.yaml if it doesn't exist, then install
RUN if [ ! -f pnpm-lock.yaml ]; then pnpm import; fi

# Configure pnpm to avoid symlinks for native modules
RUN pnpm config set node-linker hoisted
RUN pnpm config set shamefully-hoist true

# Install dependencies
RUN pnpm install --frozen-lockfile

# Fix Sharp by reinstalling with npm (better native module support)
RUN npm install sharp --platform=linux --arch=x64 --no-save

# Build stage - build the application
FROM node:20-slim AS builder
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY package.json yarn.lock* pnpm-lock.yaml* ./

# Copy source code (excluding files in .dockerignore)
COPY . ./

# Set NODE_ENV for build
ENV NODE_ENV=production

# Build Strapi admin panel
RUN pnpm run build

# Production dependencies stage
FROM node:20-slim AS prod-deps
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

COPY package.json yarn.lock* pnpm-lock.yaml* ./

# Generate pnpm-lock.yaml if it doesn't exist, then install
RUN if [ ! -f pnpm-lock.yaml ]; then pnpm import; fi

# Configure pnpm to avoid symlinks for native modules
RUN pnpm config set node-linker hoisted
RUN pnpm config set shamefully-hoist true

# Install production dependencies
RUN pnpm install --prod --frozen-lockfile

# Fix Sharp by reinstalling with npm (better native module support)
RUN npm install sharp --platform=linux --arch=x64 --no-save

# Production stage
FROM node:20-slim AS production

# Install pnpm globally
RUN npm install -g pnpm

# Create app user for security
RUN groupadd --gid 1001 nodejs && \
    useradd --uid 1001 --gid nodejs --shell /bin/bash --create-home strapi

WORKDIR /app

# Copy package files
COPY package.json yarn.lock* pnpm-lock.yaml* ./

# Copy production dependencies from prod-deps stage
COPY --from=prod-deps /app/node_modules ./node_modules

# Copy application files from builder stage
COPY --from=builder --chown=strapi:nodejs /app/build ./build
COPY --from=builder --chown=strapi:nodejs /app/config ./config
COPY --from=builder --chown=strapi:nodejs /app/database ./database
COPY --from=builder --chown=strapi:nodejs /app/src ./src
COPY --from=builder --chown=strapi:nodejs /app/public ./public
COPY --from=builder --chown=strapi:nodejs /app/favicon.png ./

# Create necessary directories with proper permissions
RUN mkdir -p .tmp public/uploads && \
    chown -R strapi:nodejs /app && \
    chmod -R 755 /app

# Set production environment variables
ENV NODE_ENV=production
ENV STRAPI_TELEMETRY_DISABLED=true

# Switch to non-root user
USER strapi

# Expose Strapi port
EXPOSE 1337

# Start Strapi directly
CMD ["pnpm", "start"]