{"name": "corporate-blog-template", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-cloud": "4.24.5", "@strapi/plugin-i18n": "4.24.5", "@strapi/plugin-seo": "^1.9.8", "@strapi/plugin-users-permissions": "4.24.5", "@strapi/strapi": "4.24.5", "better-sqlite3": "8.6.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "5.3.4", "styled-components": "5.3.3"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "42b937aa-0f16-4a30-bb55-95f60ec03b48"}, "engines": {"node": ">=16.x.x <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}