{"kind": "singleType", "collectionName": "globals", "info": {"singularName": "global", "pluralName": "globals", "displayName": "Global", "name": "global", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"metadata": {"type": "component", "repeatable": false, "component": "meta.metadata", "pluginOptions": {"i18n": {"localized": true}}}, "favicon": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"], "pluginOptions": {"i18n": {"localized": true}}}, "notificationBanner": {"type": "component", "repeatable": false, "component": "elements.notification-banner", "pluginOptions": {"i18n": {"localized": true}}}, "navbar": {"type": "component", "repeatable": false, "component": "layout.navbar", "pluginOptions": {"i18n": {"localized": true}}}, "footer": {"type": "component", "repeatable": false, "pluginOptions": {"i18n": {"localized": true}}, "component": "layout.footer"}}}