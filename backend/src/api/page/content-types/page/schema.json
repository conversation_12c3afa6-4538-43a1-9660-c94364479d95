{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Page", "name": "page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"shortName": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "metadata": {"type": "component", "repeatable": false, "component": "meta.metadata", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "contentSections": {"type": "dynamiczone", "components": ["sections.hero", "sections.bottom-actions", "sections.feature-columns-group", "sections.feature-rows-group", "sections.testimonials-group", "sections.large-video", "sections.rich-text", "sections.pricing", "sections.lead-form", "sections.features", "sections.heading", "sections.overview", "sections.separator", "sections.client-logo", "shared.media", "sections.large-image"], "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"pluginOptions": {"i18n": {"localized": false}}, "type": "string", "regex": "^$|^[a-zA-Z/-]+$"}, "heading": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "description": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}}}