{"collectionName": "components_layout_footers", "info": {"displayName": "Footer", "description": ""}, "options": {}, "attributes": {"footerLogo": {"type": "component", "repeatable": false, "component": "layout.logo"}, "menuLinks": {"type": "component", "repeatable": true, "component": "links.link"}, "legalLinks": {"type": "component", "repeatable": true, "component": "links.link"}, "socialLinks": {"type": "component", "repeatable": true, "component": "links.social-link"}, "categories": {"type": "relation", "relation": "oneToMany", "target": "api::category.category"}}}