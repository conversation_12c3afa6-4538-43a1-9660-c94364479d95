#!/bin/bash

# Deployment script with comprehensive cleanup
# Usage: ./deploy.sh [frontend|backend|all]

set -e  # Exit on any error

SERVICE=${1:-all}
TAG=${TAG:-development}

echo "🚀 Starting deployment for: $SERVICE"
echo "📦 Using tag: $TAG"

# Function to cleanup containers and images
cleanup_service() {
    local service_name=$1
    local image_name=$2
    
    echo "🧹 Cleaning up old $service_name containers..."
    
    # Stop and remove containers using docker compose
    docker compose down $service_name || true
    
    # Force stop any containers using the image
    docker stop $(docker ps -q --filter "ancestor=$image_name:$TAG") 2>/dev/null || true
    
    # Remove stopped containers
    docker container prune -f || true
    
    # Clean up temporary files for backend service
    if [ "$service_name" = "backend-eguard" ]; then
        sudo rm -rf backend/.tmp || true
        echo "🧹 Cleaned up backend .tmp directory"
    fi
    
    # Remove unused images (keep last 2 versions)
    docker images $image_name --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}" | \
        tail -n +2 | head -n -2 | awk '{print $2}' | \
        xargs -r docker rmi || true
    
    echo "✅ Cleanup completed for $service_name"
}

# Function to deploy service
deploy_service() {
    local service_name=$1
    local image_name=$2
    
    echo "📥 Pulling latest image for $service_name..."
    docker pull $image_name:$TAG
    
    echo "🚀 Starting $service_name..."
    docker compose up -d $service_name

    echo "🔍 Verifying $service_name deployment..."
    docker compose ps $service_name
    
    # Wait for service to be healthy
    echo "⏳ Waiting for $service_name to be ready..."
    sleep 10
    
    # Check if container is running
    if docker compose ps $service_name | grep -q "Up"; then
        echo "✅ $service_name deployed successfully!"
    else
        echo "❌ $service_name deployment failed!"
        docker compose logs $service_name
        exit 1
    fi
}

# Main deployment logic
case $SERVICE in
    "frontend")
        cleanup_service "frontend" "dimastw/fe-eguard-web"
        deploy_service "frontend" "dimastw/fe-eguard-web"
        ;;
    "backend")
        cleanup_service "backend-eguard" "dimastw/be-eguard-web"
        deploy_service "backend-eguard" "dimastw/be-eguard-web"
        ;;
    "all")
        cleanup_service "backend-eguard" "dimastw/be-eguard-web"
        cleanup_service "frontend" "dimastw/fe-eguard-web"
        deploy_service "backend-eguard" "dimastw/be-eguard-web"
        deploy_service "frontend" "dimastw/fe-eguard-web"
        ;;
    *)
        echo "❌ Invalid service: $SERVICE"
        echo "Usage: $0 [frontend|backend|all]"
        exit 1
        ;;
esac

echo "🎉 Deployment completed successfully!"

# Show final status
echo "📊 Final status:"
docker compose ps
