version: '3.8'

networks:
  eguard-network:
    driver: bridge

services:
  frontend:
    image: dimastw/fe-eguard-web:${TAG:-development}  # Match the tag from CI
    ports:
      - "8082:8082"  # Adjusted to match your earlier CI port
    env_file:
      - ${ENV_FILE:-.env}  # Load environment variables from .env file
    environment:
      - STRAPI_API_URL=http://backend-eguard:1337
    networks:
      - eguard-network
    # Removed depends_on to allow independent deployment

  backend-eguard:
    image: dimastw/be-eguard-web:${TAG:-development}  # Match the tag from CI
    ports:
      - "1337:1337"
    env_file:
      - ${ENV_FILE:-.env} # Load environment variables from .env file
    volumes:
      - strapi-tmp:/app/.tmp
      - strapi-uploads:/app/public/uploads
    networks:
      - eguard-network


volumes:
  postgres-data:
  strapi-tmp:
  strapi-uploads: