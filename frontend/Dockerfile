# Base stage
FROM node:18-bullseye-slim AS base
ENV NODE_ENV=production
WORKDIR /myapp

# Install pnpm globally
RUN npm install -g pnpm

# Dependencies stage (production only)
FROM base AS deps
COPY package.json package-lock.json* pnpm-lock.yaml* ./
# Generate pnpm-lock.yaml if it doesn't exist, then install
RUN if [ ! -f pnpm-lock.yaml ]; then pnpm import; fi
RUN pnpm install --prod --frozen-lockfile

# Build stage (with dev dependencies)
FROM base AS build
ENV NODE_ENV=development
# Copy package files first
COPY package.json package-lock.json* pnpm-lock.yaml* ./
# Generate pnpm-lock.yaml if it doesn't exist, then install
RUN if [ ! -f pnpm-lock.yaml ]; then pnpm import; fi
RUN pnpm install --frozen-lockfile
# Copy all frontend files
COPY . ./
# Build the application
RUN pnpm run build

# Deploy stage
FROM base AS deploy
ENV PORT=8082
ENV NODE_ENV=production
# Copy package files first
COPY package.json package-lock.json* pnpm-lock.yaml* ./
# Generate pnpm-lock.yaml if it doesn't exist, then install
RUN if [ ! -f pnpm-lock.yaml ]; then pnpm import; fi
RUN pnpm install --prod --frozen-lockfile
# Copy build artifacts
COPY --from=build /myapp/build /myapp/build
COPY --from=build /myapp/public /myapp/public
# Copy and setup start script
COPY start.sh ./
RUN chmod +x start.sh

EXPOSE 8082
CMD ["./start.sh"]