import { Link }from "@remix-run/react";

interface Category {
  id: number;
  attributes: {
    name: string;
    slug: string;
    articles: {
      data: Array<{}>;
    };
  };
}

interface Article {
  id: number;
  attributes: {
    title: string;
    slug: string;
  };
}

function selectedFilter(current: string, selected: string) {
  return current === selected
    ? "px-3 py-1 rounded-lg hover:underline bg-white text-eguard"
    : "px-3 py-1 rounded-lg hover:underline bg-white text-gray-900";
}

export default function ArticleSelect({
  categories,
  articles,
  params,
}: {
  categories: Category[];
  articles: Article[];
  params: {
    slug: string;
    category: string;
  };
}) {

  return (
    <div className="p-4 rounded-lg bg-slate-50 min-h-[365px] relative">
      <h4 className="text-xl font-semibold text-eguard">Browse By Category</h4>

      <div>
        <div className="flex flex-wrap py-6 space-x-2 border-eguard">
          {categories.map((category: Category) => {
            if (category.attributes.articles.data.length === 0) return null;
            return (
              <Link
                key={category.id}
                to={`/blog/${category.attributes.slug}`}
                prefetch="intent"
                className={selectedFilter(
                  category.attributes.slug,
                  params.category
                )}
              >
                #{category.attributes.name}
              </Link>
            );
          })}
          <Link to={"/blog"} className={selectedFilter("", "filter")} prefetch="intent">
            #all
          </Link>
        </div>

        <div className="space-y-2">
          <h4 className="text-lg font-semibold text-eguard">Other Posts You May Like</h4>
          <ul className="ml-4 space-y-1 list-disc">
            {articles.map((article: Article) => {
              return (
                <li key={article.id} className="text-black">
                  <Link
                    rel="noopener noreferrer"
                    to={`/blog/${params.category}/${article.attributes.slug}`}
                    prefetch="intent"
                    className={`${
                      params.slug === article.attributes.slug &&
                      "text--400"
                    }  hover:underline hover:text--400 transition-colors duration-200`}
                  >
                    {article.attributes.title}
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
}
