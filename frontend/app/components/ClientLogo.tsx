import { getStrapiMedia } from "../utils/api-helpers";

interface ClientLogoProps {
    data?: {
        id: number
        __component: string
        picture: {
            data: Array<{
                id: number
                attributes: {
                    url: string
                    alternativeText: string | null
                    caption: string | null
                    width: number
                    height: number
                }
            }>
        }
    }
}

export default function ClientLogo({ data }: ClientLogoProps) {
    const logos = data?.picture?.data || [];

    return (
        <div className="bg-white">
            <div className="container mx-auto flex flex-wrap justify-center items-center gap-4 md:gap-8 py-8 md:py-16">
                {logos.map((logo) => (
                <div key={logo.id} className="flex items-center justify-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6 px-4">
                    <img
                        src={getStrapiMedia(logo.attributes.url) || ""}
                        alt={logo.attributes.alternativeText || "Client logo"}
                        width={logo.attributes.width}
                        height={logo.attributes.height}
                        className="object-contain w-full h-auto max-h-16 md:max-h-24"
                    />
                </div>
            ))}
        </div>
        </div>
    );
}
