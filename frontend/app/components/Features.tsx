import { Link } from "@remix-run/react";
import { 
  Zap, 
  Star, 
  Heart,
  ShieldCheck,
  CircleCheckBig,
  TabletSmartphone,
  View,
  MessageCircleWarning,
  Lock,
  Blocks,
  ShieldUser,
  ServerCog,
  // Add other icons you need
  type LucideIcon 
} from 'lucide-react';

interface FeaturesProps {
  data: {
    heading: string;
    description: string;
    feature: FeatureResponse[];
  };
}

interface FeatureProps {
  id: string;
  title: string;
  description: string;
  showLink: boolean;
  newTab: boolean;
  url: string;
  text: string;
  icon: string;
}

interface FeatureResponse {
  id: string;
  title: string;
  description: string;
  showLink: boolean;
  newTab: boolean;
  url: string;
  text: string;
  icon: string;
}

// Create an icon map
const IconMap: Record<string, LucideIcon> = {
  Zap,
  Star,
  Heart,
  ShieldCheck,
  CircleCheckBig,
  TabletSmartphone,
  View,
  MessageCircleWarning,
  Lock,
  Blocks,
  ShieldUser,
  ServerCog,
  // Add other icons you plan to use
};

function Feature({
  title,
  description,
  showLink,
  newTab,
  url,
  text,
  icon,
}: FeatureResponse) {
  const Icon = IconMap[icon];

  return (
    <div className="flex flex-col items-center p-4">
      {Icon && (
        <Icon className="w-8 h-8 text-eguard" />
      )}
      <h3 className="my-3 text-3xl font-semibold text-center">{title}</h3>
      <div className="space-y-1 leading-tight my-6 text-justify">
        <p>{description}</p>
      </div>
      {showLink && url && text && (
        <div>
          <Link
            to={url}
            target={newTab ? "_blank" : "_self"}
            className="inline-block px-4 py-2 mt-4 text-sm font-semibold text-white transition duration-200 ease-in-out bg-violet-500 rounded-lg hover:bg-violet-600"
          >
            {text}
          </Link>
        </div>
      )}
    </div>
  );
}

export default function Features({ data }: FeaturesProps) {
  return (
    <section className="bg-white  m:py-12 lg:py-12">
      {/* <div className="container mx-auto py-4 space-y-2 text-center">
        <h2 className="text-5xl font-bold text-center">{data.heading}</h2>
        <p className="text-gray-400">{data.description}</p>
      </div> */}
      <div className="container mx-auto my-6 grid justify-center gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {data.feature.map((feature: FeatureProps, index: number) => (
          <Feature key={index} {...feature} />
        ))}
      </div>
    </section>
  );
}
