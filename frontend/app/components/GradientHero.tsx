import HighlightedText from "./HighlightedText"

interface HeroProps {
    data?: {
      id: string
      title: string
      description: string
      height?: string // New height prop
    }
  }
  
  export default function GradientHero({ data}: HeroProps) {

    console.log(data);
    // Default values if data is not provided
    const title = data?.title || "Default Title"
    const description = data?.description || "Default description text"
    
    return (
      <section
        className="w-full flex items-center justify-center pt-20"
        style={{
          background: "linear-gradient(90deg, #D7E5F4, #FEFEFE)",
          minHeight: data?.height ?? '50vh',
        }}
      >
        <div 
          className="container px-4 mx-auto text-center"
          style={{ paddingTop: data?.height, paddingBottom: data?.height }}
        >
          {/* <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 mb-4">{title}</h1> */}
          {/* <p className="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto">{description}</p> */}
          <HighlightedText
            text={title}
            tag="h1"
            className="text-5xl font-bold leading-none sm:text-6xl mb-8"
            color="text-eguard"
          />

          <HighlightedText
            text={description}
            tag="p"
            className="tmt-6 mb-8 text-lg sm:mb-12"
            color="text-eguard"
          />
        </div>
      </section>
    )
  }
  
  