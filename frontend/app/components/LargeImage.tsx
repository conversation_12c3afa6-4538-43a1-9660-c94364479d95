import { getStrapiMedia } from "../utils/api-helpers";

interface MediaProps {
  picture: {
    data: {
      id: string;
      attributes: {
        url: string;
        name: string;
        alternativeText: string;
      };
    };
  };
}

export default function LargeImage({ data }: { data: MediaProps }) {
  const imgUrl = getStrapiMedia(data.picture.data.attributes.url);
  return (
    <div className="w-2/3 mx-auto p-4">
      <img
      src={imgUrl || ""}
      alt={data.picture.data.attributes.alternativeText || "none provided"}
      className="object-cover w-full h-full rounded-md overflow-hidden"
      />
    </div>
  );
}