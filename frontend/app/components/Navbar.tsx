import Logo from "./Logo";
import type { StrapiUserResponse } from "~/types";
import { Link, useLocation } from "@remix-run/react";
import { Avatar } from "~/components/Avatar";
import { Logout } from "~/routes/logout";
import { useState } from 'react';

interface NavLinkData {
  id: number;
  url: string;
  newTab: boolean;
  text: string;
}

function isRouteMatch(url: string, pathname: string) {
  return pathname === url;
}

function NavLink({ url, text }: NavLinkData) {
  const { pathname } = useLocation();
  const isActive = isRouteMatch(url, pathname);

  return (

    <>
      <li className={`nav-item mt-5 ${isActive ? 'active' : ''}`}>
        <Link
          to={url}
          className="nav-link"
        >
          <div className="relative">
            <div className={`z-50 relative text-center ${isRouteMatch(url, pathname) ? 'text-black' : 'text-gray-100'}`}>
              <strong>{text}</strong>
            </div>
            {/* <img 
                src="/nav.svg" 
                className="absolute z-40 top-2 " 
                style={{ display: isRouteMatch(url, pathname) ? 'block' : 'none' }}
            /> */}
          </div>
        </Link>
      </li>

    </>

  );
}

export default function Navbar({
  links,
  logoUrl,
  logoText,
  user,
}: {
  links: Array<NavLinkData>;
  logoUrl: string | null;
  logoText: string | null;
  user: StrapiUserResponse | null;
}) {
  return (
    <div className="navbar bg-eguard z-50">
      <div className="container flex h-16 mx-auto px-0 sm:px-6 items-center justify-between relative">
        {/* Logo di kiri */}
        <div className="flex items-center">
          <Logo src={logoUrl}>
            {logoText && <h2 className="text-2xl font-bold">{logoText}</h2>}
          </Logo>
        </div>

        {/* NavLink di tengah */}
        <div className="absolute left-1/2 -translate-x-1/2 hidden lg:flex">

          <ul className="nav-list">
            {links.map((item: NavLinkData) => (
              <NavLink key={item.id} {...item} />
            ))}
          </ul>
        </div>

        {/* Tombol menu untuk mobile */}
        <button className="p-4 lg:hidden">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6 text-gray-100"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>

      <style>{`
        .navbar {
          position: fixed;
          left: 0;
          top: 0;
          width: 100%;
        }
        .nav-list {
          display: flex;
          list-style: none;
          margin: 0;
          padding-left: 40px;
        }
        .nav-item {
          padding: 10px 30px;
          font-size: 1.2rem;
          border-radius: 20px 20px 0 0;
          position: relative;
        }
        // .nav-item.active {
        //   background: #fff;
        // }
     
      `}</style>
    </div>
  );
}
