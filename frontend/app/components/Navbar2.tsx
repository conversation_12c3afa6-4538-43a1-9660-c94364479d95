import Logo from "./Logo";
import type { StrapiUserResponse } from "~/types";
import { Link, useLocation } from "@remix-run/react";
import { Avatar } from "~/components/Avatar";
import { Logout } from "~/routes/logout";
import { useState } from 'react';

interface NavLinkData {
  id: number;
  url: string;
  newTab: boolean;
  text: string;
}

function isRouteMatch(url: string, pathname: string) {
  return pathname === url;
}

function NavLink({ url, text }: NavLinkData) {
  const { pathname } = useLocation();
  const isActive = isRouteMatch(url, pathname);
  
  return (
    <li className={`nav-item mt-5 ${isActive ? 'active' : ''}`}>
      <Link
        to={url}
        className="nav-link"
      >
        {text}
      </Link>
    </li>
  );
}

export default function Navbar({
  links,
  logoUrl,
  logoText,
  user,
}: {
  links: Array<NavLinkData>;
  logoUrl: string | null;
  logoText: string | null;
  user: StrapiUserResponse | null;
}) {
  return (
    <div className="navbar bg-eguard">
      <div className="container flex h-16 mx-auto px-0 sm:px-6 items-center justify-between relative">
        {/* Logo di kiri */}
        <div className="flex items-center">
          <Logo src={logoUrl}>
            {logoText && <h2 className="text-2xl font-bold">{logoText}</h2>}
          </Logo>
        </div>

        {/* NavLink di tengah */}
        <div className="absolute left-1/2 -translate-x-1/2 hidden lg:flex">
          <ul className="nav-list">
            {links.map((item: NavLinkData) => (
              <NavLink key={item.id} {...item} />
            ))}
          </ul>
        </div>

        {/* Tombol menu untuk mobile */}
        <button className="p-4 lg:hidden">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6 text-gray-100"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>

      <style>{`
        .navbar {
          position: fixed;
          left: 0;
          top: 0;
          width: 100%;
        }
        .nav-list {
          display: flex;
          list-style: none;
          margin: 0;
          padding-left: 40px;
        }
        .nav-item {
          padding: 10px 30px;
          font-size: 1rem;
          border-radius: 20px 20px 0 0;
          position: relative;
        }
        .nav-item.active {
          background: #fff;
        }
        .nav-link {
          text-decoration: none;
          color: #fff;
        }
        .nav-item.active .nav-link {
          color: #177DFA;
        }
        .nav-item.active .nav-link::before {
          content: "";
          left: -30px;
          bottom: 0;
          height: 30px;
          width: 30px;
          position: absolute;
          background: #177DFA;
          border-radius: 50%;
          box-shadow: 15px 15px 0 transparent;
        }
        .nav-item.active .nav-link::after {
          content: "";
          right: -30px;
          bottom: 0;
          height: 30px;
          width: 30px;
          position: absolute;
          background: #177DFA;
          border-radius: 50%;
          box-shadow: -15px 15px 0 transparent;
        }
      `}</style>
    </div>
  );
}
