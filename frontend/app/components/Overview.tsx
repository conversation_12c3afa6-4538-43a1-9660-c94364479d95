import { getStrapiMedia } from "../utils/api-helpers";

interface OverviewProps {
    data?: {
        id: string
        title: string
        description: string
        picture: {
            data: {
                id: string
                attributes: {
                    name: string
                    alternativeText: string
                    url: string
                }
            }
        }
    }
}

export default function Overview({ data }: OverviewProps) {

    const title = data?.title || "Default Title"
    const description = data?.description || "Default description text"
    const imageUrl = getStrapiMedia(data?.picture?.data?.attributes?.url || "");

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 relative p-4 bg-white">
            <div className="relative px-5">
                <div className="overflow-hidden">
                    <img 
                        src={imageUrl || ""} 
                        alt={data?.picture?.data?.attributes?.alternativeText} 
                        className="w-full object-cover -top-24 rounded-lg py-10" 
                    />
                </div>
            </div>
            <div className="flex flex-col justify-center py-12 px-10">
                <h2 className="text-center text-2xl font-bold mb-4">{title}</h2>
                <p className="text-justify">{description}</p>
            </div>
        </div>
    )
}

