interface SeparatorProps {
    data?: {
        className?: string;
        title: string;
    }
}

export default function Separator({ data }: SeparatorProps) {
    const className = data?.className || "";
    const title = data?.title || "";

    return (
        <div className={`relative w-full bg-[#1b232c] ${className}`}>
            <svg id="wave" style={{ transform: 'rotate(0deg)', transition: '0.3s' }} viewBox="0 0 1440 100" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="sw-gradient-0" x1="0" x2="0" y1="1" y2="0">
                        <stop stopColor="rgba(0, 0, 0, 1)" offset="0%" />
                        <stop stopColor="rgba(22, 30, 38, 1)" offset="100%" />
                    </linearGradient>
                </defs>
                <path style={{ transform: 'translate(0, 0px)', opacity: 1 }} fill="url(#sw-gradient-0)" d="M0,54L34.3,54C68.6,54,137,54,206,63C274.3,72,343,90,411,96C480,102,549,96,617,90C685.7,84,754,78,823,69C891.4,60,960,48,1029,63C1097.1,78,1166,120,1234,123C1302.9,126,1371,90,1440,84C1508.6,78,1577,102,1646,102C1714.3,102,1783,78,1851,63C1920,48,1989,42,2057,36C2125.7,30,2194,24,2263,42C2331.4,60,2400,102,2469,126C2537.1,150,2606,156,2674,132C2742.9,108,2811,54,2880,27C2948.6,0,3017,0,3086,27C3154.3,54,3223,108,3291,117C3360,126,3429,90,3497,90C3565.7,90,3634,126,3703,138C3771.4,150,3840,138,3909,114C3977.1,90,4046,54,4114,60C4182.9,66,4251,114,4320,126C4388.6,138,4457,114,4526,108C4594.3,102,4663,114,4731,117C4800,120,4869,114,4903,111L4937.1,108L4937.1,180L4902.9,180C4868.6,180,4800,180,4731,180C4662.9,180,4594,180,4526,180C4457.1,180,4389,180,4320,180C4251.4,180,4183,180,4114,180C4045.7,180,3977,180,3909,180C3840,180,3771,180,3703,180C3634.3,180,3566,180,3497,180C3428.6,180,3360,180,3291,180C3222.9,180,3154,180,3086,180C3017.1,180,2949,180,2880,180C2811.4,180,2743,180,2674,180C2605.7,180,2537,180,2469,180C2400,180,2331,180,2263,180C2194.3,180,2126,180,2057,180C1988.6,180,1920,180,1851,180C1782.9,180,1714,180,1646,180C1577.1,180,1509,180,1440,180C1371.4,180,1303,180,1234,180C1165.7,180,1097,180,1029,180C960,180,891,180,823,180C754.3,180,686,180,617,180C548.6,180,480,180,411,180C342.9,180,274,180,206,180C137.1,180,69,180,34,180L0,180Z" />
            </svg>

            {/* Centered Title */}
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white text-5xl font-semibold">{title}</span>
            </div>
        </div>
    );
}
