import type { MetaFunction } from "@remix-run/node";
import { Link, useLoaderData, useLocation } from "@remix-run/react";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { sectionRenderer } from "~/utils/section-renderer";
import GradientHero from "~/components/GradientHero";

export const meta: MetaFunction = () => {
  return [{ title: "New Remix App" }];
};

export async function loader() {
  // Fetch current page
  const path = `/pages`;
  const slug = "about";
  const urlParamsObject = { filters: { slug } };
  const currentPage = await fetchStrapiData(path, urlParamsObject);

  // Fetch all about-related pages
  const allPagesPath = `/pages`;
  const allPagesParams = { filters: { slug: { $startsWith: 'about-' } } };
  const subPages = await fetchStrapiData(allPagesPath, allPagesParams);
    console.log("subPages", subPages);
  return { currentPage, subPages };
}

export default function AboutRoute() {
  const { currentPage, subPages } = useLoaderData<typeof loader>();
  const location = useLocation();
  const isOverview = location.pathname === '/about';

  if (currentPage.data.length === 0) return <div className="container mx-auto p-8 text-white">Please publish your first page from Strapi Admin</div>;

  const contentSections = currentPage.data[0].attributes.contentSections;

  const heroData = {
    id: "news-hero",
    title: "[About]",
    description: "Learn more about us",
    height: "2vh"
  };


  return (
    <div className=" mx-auto">


      <GradientHero data={heroData} />

      <nav className="flex gap-4 p-4 bg-gray-800 text-white" style={{
        background: "linear-gradient(270deg, #D7E5F4, #FEFEFE)"
      }}>

        <div className="container mx-auto text-center space-x-8">
          
          <Link
            to="/about"
            className={`hover:text-gray-300 ${isOverview ? 'text-blue-400' : ''}`}
          >
            Overview
          </Link>
          {subPages.data.map((page: any) => (
            <Link
              key={page.attributes.slug}
              to={`/about/${page.attributes.slug.replace('about-', '')}`}
              className={`hover:text-gray-300 ${location.pathname === `/about/${page.attributes.slug.replace('about-', '')}`
            ? 'text-blue-400'
            : ''
          }`}
            >
              {page.attributes.title || page.attributes.slug.replace('about-', '')}
            </Link>

          ))}
        </div>
      </nav>
      <div className="content">
        {contentSections.map((section: any, index: number) =>
          sectionRenderer(section, index)
        )}
      </div>
    </div>
  );
}
