import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import PageHeader from "~/components/PageHeader";
import BlogList from "~/components/BlogList";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { getUserData } from "~/utils/session.server";
import GradientHero from "~/components/GradientHero";
export async function loader({ request }: LoaderFunctionArgs ) {
  const path = `/news`;
  
  const path2 = `/articles`;

  const urlParamsObject = {
    sort: { createdAt: "desc" },
    populate: {
      cover: { fields: ["url"] },
      category: { populate: "*" },
      authorsBio: {
        populate: "*",
      },
    },
  };

  const user = await getUserData(request);
  const response = await fetchStrapiData(path2, urlParamsObject, user ? user.jwt : null);
  return json(response);
}

export default function BlogRoute() {
  const data = useLoaderData<typeof loader>();
  const heroData = {
    id: "news-hero",
    title: "[Latest] [News]",
    description: "Stay informed with our latest articles, insights and company updates",
    height: "2vh"
  };
  
  return (
    <div>
      <GradientHero data={heroData}/>  
      <BlogList data={data.data} />
    </div>
  );
}
