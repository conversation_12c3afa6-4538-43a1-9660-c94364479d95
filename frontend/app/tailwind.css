@tailwind base;
@tailwind components;
@tailwind utilities;


/******************************************* 
  Rich Text Styles
*******************************************/ 

/* Headers */
.rich-text article  h1 {
  @apply text-4xl font-bold mb-4 text-eguard;
}

.rich-text article  h2 {
  @apply text-3xl font-bold mb-4 text-eguard;
}

.rich-text article  h3 {
  @apply text-2xl font-bold mb-4 text-eguard;
}

.rich-text article  h4 {
  @apply text-xl font-bold mb-4 text-eguard;
}

.rich-text article  h5 {
  @apply text-lg font-bold mb-4 text-eguard;
}

.rich-text article  h6 {
  @apply text-base font-bold mb-4 text-violet-200;
}

/* Horizontal rules */
.rich-text article  hr {
  @apply border-gray-300 my-8;
}

.rich-text article  a {
  @apply text-eguard underline;
}

/* Typographic replacements */
.rich-text article  p {
  @apply mb-4;
}

/* Emphasis */
.rich-text article  strong {
  @apply font-bold;
}

.rich-text article  em {
  @apply italic;
}

.rich-text article  del {
  @apply line-through;
}

/* Blockquotes */
.rich-text article  blockquote {
  @apply border-l-4 border-gray-400 pl-4 py-2 mb-4;
}

/* Lists */
.rich-text article  ul {
  @apply list-disc pl-4 mb-4;
}

.rich-text article  ol {
  @apply list-decimal pl-4 mb-4;
}

.rich-text article  li {
  @apply mb-2;
}

.rich-text article  li > ul {
  @apply list-disc pl-4 mb-2;
}

.rich-text article  li > ol {
  @apply list-decimal pl-4 mb-2;
}

/* Code blocks */
.rich-text article  pre {
  @apply font-mono bg-gray-900 text-gray-100 rounded p-4  my-6;
}

.rich-text article  code {
  @apply font-mono bg-gray-900 text-gray-100 rounded px-2 py-1;
}

/* Tables */
.rich-text article  table {
  @apply w-full border-collapse border-gray-300 my-6;
}

.rich-text article  th {
  @apply bg-gray-900 text-left py-2 px-4 font-semibold border-b border-gray-300;
}

.rich-text article  td {
  @apply py-2 px-4 border-b border-gray-300;
}

/* Images */
.rich-text article  img {
  @apply w-full object-cover rounded-xl my-6;
}

/* Custom containers */
.rich-text article  .warning {
  @apply bg-yellow-100 border-yellow-500 text-yellow-700 px-4 py-2 rounded-lg mb-4;
}


/******************************************* 
  React Slideshow Styles
*******************************************/   

.react-slideshow-container [aria-label="Previous Slide"] {
  margin-left: 1rem;
}

.react-slideshow-container [aria-label="Next Slide"] {
  margin-right: 1rem;
}

/*---break---*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/*---break---*/

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}