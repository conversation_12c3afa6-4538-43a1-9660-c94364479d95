import Hero from "../components/Hero";
import GradientHero from "../components/GradientHero";
import Features from "../components/Features";
import Testimonials from "../components/Testimonials";
import Pricing from "../components/Pricing";
import Email from "../components/Email";
import Overview from "../components/Overview";
import Separator from "../components/Separator";
import ClientLogo from "../components/ClientLogo";
import LargeImage from "~/components/LargeImage";
export function sectionRenderer(section: any, index: number) {


  switch (section.__component) {
    case "sections.hero":
      // return <Hero key={index} data={section} />;
      return <GradientHero key={index} data={section} />;
    case "sections.features":
      return <Features key={index} data={section} />;
    case "sections.testimonials-group":
      return <Testimonials key={index} data={section} />;
    case "sections.pricing":
      return <Pricing key={index} data={section} />;
    case "sections.lead-form":
      return <Email key={index} data={section} />;
    case "sections.overview":
      return <Overview key={index} data={section} />;
    case "sections.separator":
      return <Separator key={index} data={section} />;
    case "sections.client-logo":
      return <ClientLogo key={index} data={section} />;
    case "sections.large-image":
      console.log("Media section", section);
      return <LargeImage key={index} data={section} />;
      
      // return <img key={index} src={section.url} alt={section.alt || "Media"} />;
    default:
      return null;
  }
}