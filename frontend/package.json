{"name": "frontend", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix build", "dev": "remix dev --manual", "start": "NODE_ENV=production remix-serve ./build/index.js", "typecheck": "tsc"}, "dependencies": {"@markdoc/markdoc": "^0.4.0", "@remix-run/css-bundle": "^2.3.1", "@remix-run/node": "^2.3.1", "@remix-run/react": "^2.3.1", "@remix-run/serve": "^2.3.1", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.1.1", "isbot": "^3.7.1", "lucide-react": "^0.482.0", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-slideshow-image": "^4.3.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@remix-run/dev": "^2.3.1", "@remix-run/eslint-config": "^2.3.1", "@types/qs": "^6.9.10", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}