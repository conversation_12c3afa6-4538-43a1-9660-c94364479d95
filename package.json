{"name": "eguard-web", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"frontend": "yarn dev --prefix ../frontend/", "backend": "yarn dev --prefix ../backend/", "setup:frontend": "cd frontend && yarn && node ../copy-env.js ./", "setup:backend": "cd backend && yarn && node ../copy-env.js ./", "setup": "yarn install && yarn setup:frontend && yarn setup:backend", "dev": "yarn concurrently \"cd backend && yarn develop\" \"cd frontend && yarn dev\"", "repo:upstream": "git fetch upstream && git merge upstream/main", "seed": "cd backend && yarn strapi import -f ../seed-data.tar.gz", "export": "cd backend && yarn strapi export --no-encrypt -f  ../seed-data"}, "dependencies": {"concurrently": "^7.6.0"}, "author": "", "license": "ISC"}