This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
backend/
  .strapi/
    client/
      app.js
      index.html
  .vscode/
    settings.json
  config/
    admin.js
    api.js
    database.js
    middlewares.js
    plugins.js
    server.js
  public/
    robots.txt
  src/
    admin/
      app.example.js
      webpack.config.example.js
    api/
      article/
        content-types/
          article/
            schema.json
        controllers/
          article.js
        middlewares/
          articles-middleware.js
        routes/
          article.js
        services/
          article.js
      author/
        content-types/
          author/
            schema.json
        controllers/
          author.js
        routes/
          author.js
        services/
          author.js
      category/
        content-types/
          category/
            schema.json
        controllers/
          category.js
        routes/
          category.js
        services/
          category.js
      global/
        content-types/
          global/
            schema.json
        controllers/
          global.js
        routes/
          global.js
        services/
          global.js
      lead-form-submission/
        content-types/
          lead-form-submission/
            schema.json
        controllers/
          lead-form-submission.js
        routes/
          lead-form-submission.js
        services/
          lead-form-submission.js
      page/
        content-types/
          page/
            schema.json
        controllers/
          page.js
        middlewares/
          page-populate-middleware.js
        routes/
          page.js
        services/
          page.js
      product-feature/
        content-types/
          product-feature/
            schema.json
        controllers/
          product-feature.js
        routes/
          product-feature.js
        services/
          product-feature.js
    components/
      elements/
        feature-column.json
        feature-row.json
        feature.json
        footer-section.json
        logos.json
        notification-banner.json
        plan.json
        testimonial.json
      layout/
        footer.json
        logo.json
        navbar.json
      links/
        button-link.json
        button.json
        link.json
        social-link.json
      meta/
        metadata.json
      sections/
        bottom-actions.json
        client-logo.json
        feature-columns-group.json
        feature-rows-group.json
        features.json
        heading.json
        hero.json
        large-image.json
        large-video.json
        lead-form.json
        overview.json
        pricing.json
        rich-text.json
        separator.json
        testimonials-group.json
      shared/
        media.json
        quote.json
        rich-text.json
        seo.json
        slider.json
        video-embed.json
    extensions/
      users-permissions/
        content-types/
          user/
            schema.json
    index.js
  types/
    generated/
      components.d.ts
      contentTypes.d.ts
  .editorconfig
  .env.example
  .eslintignore
  .eslintrc
  .gitignore
  package.json
  README.md
frontend/
  app/
    api/
      auth/
        login.server.ts
        register.server.ts
        userme.server.ts
      delete-image.server.ts
      fetch-strapi-data.server.ts
      form.server.ts
      update-user-profile.server.ts
      upload-image.server.ts
    components/
      ArticleSelect.tsx
      Avatar.tsx
      Banner.tsx
      BlogList.tsx
      ClientLogo.tsx
      Email.tsx
      Error.tsx
      Features.tsx
      Footer.tsx
      Footer2.tsx
      GradientHero.tsx
      Hero.tsx
      HighlightedText.tsx
      ImageField.tsx
      ImageSlider.tsx
      LargeImage.tsx
      Loader.tsx
      LoginForm.tsx
      Logo.tsx
      Media.tsx
      Navbar.tsx
      Navbar2.tsx
      Overview.tsx
      PageHeader.tsx
      Post.tsx
      Pricing.tsx
      Quote.tsx
      RegisterForm.tsx
      RichText.tsx
      Separator.tsx
      SubmitButton.tsx
      Testimonials.tsx
      UserProfileForm.tsx
      VideoEmbed.tsx
    lib/
      utils.ts
    routes/
      _index.tsx
      about._index.tsx
      api.join-form.tsx
      blog.$category.tsx
      login.tsx
      logout.tsx
      news._index.tsx
      news.$category._index.tsx
      news.$category.$slug._index.tsx
      news.$category.$slug.tsx
      profile.tsx
      register.tsx
    utils/
      api-helpers.ts
      post-renderer.tsx
      render-button-style.ts
      section-renderer.tsx
      session.server.ts
    entry.client.tsx
    entry.server.tsx
    env.server.ts
    root.tsx
    tailwind.css
    types.ts
  public/
    nav.svg
  .env.example
  .eslintrc.cjs
  .gitignore
  components.json
  package.json
  postcss.config.js
  README.md
  remix.config.js
  remix.env.d.ts
  tailwind.config.ts
  tsconfig.json
.gitignore
copy-env.js
package.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="backend/src/components/sections/large-image.json">
{
  "collectionName": "components_sections_large_images",
  "info": {
    "displayName": "Large image",
    "icon": "cube",
    "description": ""
  },
  "options": {},
  "attributes": {
    "picture": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": [
        "images",
        "files",
        "videos",
        "audios"
      ]
    }
  }
}
</file>

<file path="frontend/app/components/Footer2.tsx">
import { Link } from "@remix-run/react";
import Logo from "./Logo";
import { useLocation } from "@remix-run/react";
import { CgWebsite } from "react-icons/cg/index.js";
import { FaDiscord } from "react-icons/fa/index.js";
import { AiFillTwitterCircle, AiFillYoutube } from "react-icons/ai/index.js";

interface FooterLinkData {
  id: number;
  url: string;
  newTab: boolean;
  text: string;
  social?: string;
}

interface CategoryLinkData {
  id: string;
  attributes: {
    name: string;
    slug: string;
  };
}

function FooterLink({ url, text }: FooterLinkData) {
  const { pathname } = useLocation();
  return (
    <li className="flex">
      <Link
        to={url}
        prefetch="intent"
        className={`hover:text-violet-400 ${
          pathname === url && "text-violet-400 border-violet-400"
        }}`}
      >
        {text}
      </Link>
    </li>
  );
}

function CategoryLink({ attributes }: CategoryLinkData) {
  return (
    <li className="flex">
      <Link
        to={`/blog/${attributes.slug}`}
        prefetch="intent"
        className="hover:text-violet-400"
      >
        {attributes.name}
      </Link>
    </li>
  );
}

function RenderSocialIcon({ social }: { social: string | undefined }) {
  switch (social) {
    case "WEBSITE":
      return <CgWebsite />;
    case "TWITTER":
      return <AiFillTwitterCircle />;
    case "YOUTUBE":
      return <AiFillYoutube />;
    case "DISCORD":
      return <FaDiscord />;
    default:
      return null;
  }
}

export default function Footer({
  logoUrl,
  logoText,
  menuLinks,
  categoryLinks,
  legalLinks,
  socialLinks,
}: {
  logoUrl: string | null;
  logoText: string | null;
  menuLinks: Array<FooterLinkData>;
  categoryLinks: Array<CategoryLinkData>;
  legalLinks: Array<FooterLinkData>;
  socialLinks: Array<FooterLinkData>;
}) {
  return (
    <footer className="py-6 bg-white text-black">
      <div className="container px-6 mx-auto space-y-6 divide-y divide-gray-400 md:space-y-12 divide-opacity-50">
        <div className="grid grid-cols-12">
          <div className="pb-6 col-span-full md:pb-0 md:col-span-6">
            <Logo src={logoUrl}>
              {logoText && <h2 className="text-2xl font-bold">{logoText}</h2>}
            </Logo>
          </div>

          <div className="col-span-6 text-center md:text-left md:col-span-3">
            <p className="pb-1 text-lg font-medium">Categories</p>
            <ul>
              {categoryLinks.map((link: CategoryLinkData) => (
                <CategoryLink key={link.id} {...link} />
              ))}
            </ul>
          </div>

          <div className="col-span-6 text-center md:text-left md:col-span-3">
            <p className="pb-1 text-lg font-medium">Menu</p>
            <ul>
              {menuLinks.map((link: FooterLinkData) => (
                <FooterLink key={link.id} {...link} />
              ))}
            </ul>
          </div>
        </div>
        <div className="grid justify-center pt-6 lg:justify-between">
          <div className="flex">
            <span className="mr-2">
              ©{new Date().getFullYear()} All rights reserved
            </span>
            <ul className="flex">
              {legalLinks.map((link: FooterLinkData) => (
                <Link
                  to={link.url}
                  className="text-gray-400 hover:text-gray-300 mr-2"
                  key={link.id}
                >
                  {link.text}
                </Link>
              ))}
            </ul>
          </div>
          <div className="flex justify-center pt-4 space-x-4 lg:pt-0 lg:col-end-13">
            {socialLinks.map((link: FooterLinkData) => {
              return (
                <a
                  key={link.id}
                  rel="noopener noreferrer"
                  href={link.url}
                  title={link.text}
                  target={link.newTab ? "_blank" : "_self"}
                  className="flex items-center justify-center w-10 h-10 rounded-full bg-violet-400 text-gray-900"
                >
                  <RenderSocialIcon social={link.social} />
                </a>
              );
            })}
          </div>
        </div>
      </div>
    </footer>
  );
}
</file>

<file path="frontend/app/components/LargeImage.tsx">
import { getStrapiMedia } from "../utils/api-helpers";

interface MediaProps {
  picture: {
    data: {
      id: string;
      attributes: {
        url: string;
        name: string;
        alternativeText: string;
      };
    };
  };
}

export default function LargeImage({ data }: { data: MediaProps }) {
  const imgUrl = getStrapiMedia(data.picture.data.attributes.url);
  return (
    <div className="w-2/3 mx-auto p-4">
      <img
      src={imgUrl || ""}
      alt={data.picture.data.attributes.alternativeText || "none provided"}
      className="object-cover w-full h-full rounded-md overflow-hidden"
      />
    </div>
  );
}
</file>

<file path="frontend/app/routes/about._index.tsx">
import type { MetaFunction } from "@remix-run/node";
import { Link, useLoaderData, useLocation } from "@remix-run/react";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { sectionRenderer } from "~/utils/section-renderer";
import GradientHero from "~/components/GradientHero";

export const meta: MetaFunction = () => {
  return [{ title: "New Remix App" }];
};

export async function loader() {
  // Fetch current page
  const path = `/pages`;
  const slug = "about";
  const urlParamsObject = { filters: { slug } };
  const currentPage = await fetchStrapiData(path, urlParamsObject);

  // Fetch all about-related pages
  const allPagesPath = `/pages`;
  const allPagesParams = { filters: { slug: { $startsWith: 'about-' } } };
  const subPages = await fetchStrapiData(allPagesPath, allPagesParams);
    console.log("subPages", subPages);
  return { currentPage, subPages };
}

export default function AboutRoute() {
  const { currentPage, subPages } = useLoaderData<typeof loader>();
  const location = useLocation();
  const isOverview = location.pathname === '/about';

  if (currentPage.data.length === 0) return <div className="container mx-auto p-8 text-white">Please publish your first page from Strapi Admin</div>;

  const contentSections = currentPage.data[0].attributes.contentSections;

  const heroData = {
    id: "news-hero",
    title: "[About]",
    description: "Learn more about us",
    height: "2vh"
  };


  return (
    <div className=" mx-auto">


      <GradientHero data={heroData} />

      <nav className="flex gap-4 p-4 bg-gray-800 text-white" style={{
        background: "linear-gradient(270deg, #D7E5F4, #FEFEFE)"
      }}>

        <div className="container mx-auto text-center space-x-8">
          
          <Link
            to="/about"
            className={`hover:text-gray-300 ${isOverview ? 'text-blue-400' : ''}`}
          >
            Overview
          </Link>
          {subPages.data.map((page: any) => (
            <Link
              key={page.attributes.slug}
              to={`/about/${page.attributes.slug.replace('about-', '')}`}
              className={`hover:text-gray-300 ${location.pathname === `/about/${page.attributes.slug.replace('about-', '')}`
            ? 'text-blue-400'
            : ''
          }`}
            >
              {page.attributes.title || page.attributes.slug.replace('about-', '')}
            </Link>

          ))}
        </div>
      </nav>
      <div className="content">
        {contentSections.map((section: any, index: number) =>
          sectionRenderer(section, index)
        )}
      </div>
    </div>
  );
}
</file>

<file path="frontend/app/routes/news._index.tsx">
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import PageHeader from "~/components/PageHeader";
import BlogList from "~/components/BlogList";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { getUserData } from "~/utils/session.server";
import GradientHero from "~/components/GradientHero";
export async function loader({ request }: LoaderFunctionArgs ) {
  const path = `/news`;
  
  const path2 = `/articles`;

  const urlParamsObject = {
    sort: { createdAt: "desc" },
    populate: {
      cover: { fields: ["url"] },
      category: { populate: "*" },
      authorsBio: {
        populate: "*",
      },
    },
  };

  const user = await getUserData(request);
  const response = await fetchStrapiData(path2, urlParamsObject, user ? user.jwt : null);
  return json(response);
}

export default function BlogRoute() {
  const data = useLoaderData<typeof loader>();
  const heroData = {
    id: "news-hero",
    title: "[Latest] [News]",
    description: "Stay informed with our latest articles, insights and company updates",
    height: "2vh"
  };
  
  return (
    <div>
      <GradientHero data={heroData}/>  
      <BlogList data={data.data} />
    </div>
  );
}
</file>

<file path="frontend/app/routes/news.$category._index.tsx">
import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { getUserData } from "~/utils/session.server";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";

import BlogList from "~/components/BlogList";
import PageHeader from "~/components/PageHeader";

export async function loader({ params, request }: LoaderFunctionArgs) {
  const path = `/articles`;
  
  const urlParamsObject = {
    sort: { createdAt: "desc" },
    filters: {
      category: {
        slug: params.category,
      },
    },
    populate: {
      cover: { fields: ["url"] },
      category: {
        populate: "*",
      },
      authorsBio: {
        populate: "*",
      },
    },
  };

  const user = await getUserData(request);
  const response = await fetchStrapiData(path, urlParamsObject, user ? user.jwt : null);
  return json({ ...response, category: params.category });
}

export default function CategoryPostsRoute() {
  const data = useLoaderData<typeof loader>();
  return (
    <div>
      <PageHeader heading={data.category} text="Checkout Something Cool" />
      <BlogList data={data.data} />
    </div>
  );
}
</file>

<file path="frontend/app/routes/news.$category.$slug._index.tsx">
import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getUserData } from "~/utils/session.server";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { useLoaderData } from "@remix-run/react";
import Post from "~/components/Post";

export async function loader({ params, request }: LoaderFunctionArgs) {
  const slug = params.slug;
  const path = `/articles`;
  const urlParamsObject = {
    filters: { slug },
    populate: {
      cover: { fields: ["url"] },
      authorsBio: { populate: "*" },
      category: { fields: ["name"] },
      blocks: { populate: "*" },
    },
  };

  const user = await getUserData(request);
  const response = await fetchStrapiData(path, urlParamsObject, user ? user.jwt : null);
  return json({ ...response });
}

export default function PostRoute() {
  const data = useLoaderData<typeof loader>();
  if (data.data?.length === 0) return <h2>no post found</h2>;
  return <Post data={data.data[0]} />;
}
</file>

<file path="frontend/app/routes/news.$category.$slug.tsx">
import { json } from "@remix-run/node";
import { Outlet, useLoaderData } from "@remix-run/react";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import ArticleSelect from "~/components/ArticleSelect";

export async function loader({ params }: { params: { category: string } }) {
  const filter = params.category;

  const categoriesResponse = await fetchStrapiData("/categories", {
    populate: "*",
  });

  const articlesResponse = await fetchStrapiData(
    "/articles",
    filter
      ? {
          filters: {
            category: {
              name: filter,
            },
          },
        }
      : {},
  );

  return json({
    articles: articlesResponse.data,
    categories: categoriesResponse.data,
    params,
  });
}

export default function SlugRoute() {
  const data = useLoaderData<typeof loader>();
  const { articles, categories, params } = data;
  return (
    <section className="container p-8 mx-auto space-y-6 sm:space-y-12">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 lg:gap-4">
        <div className="col-span-2">
          <Outlet />
        </div>
        <aside>
          <ArticleSelect
            categories={categories}
            articles={articles}
            params={params}
          />
        </aside>
      </div>
    </section>
  );
}
</file>

<file path="backend/.strapi/client/app.js">
/**
 * This file was automatically generated by Strapi.
 * Any modifications made will be discarded.
 */
import strapiCloud from "@strapi/plugin-cloud/strapi-admin";
import i18N from "@strapi/plugin-i18n/strapi-admin";
import seo from "@strapi/plugin-seo/strapi-admin";
import usersPermissions from "@strapi/plugin-users-permissions/strapi-admin";
import { renderAdmin } from "@strapi/strapi/admin";

renderAdmin(document.getElementById("strapi"), {
  plugins: {
    "strapi-cloud": strapiCloud,
    i18n: i18N,
    seo: seo,
    "users-permissions": usersPermissions,
  },
});
</file>

<file path="backend/.strapi/client/index.html">
<!DOCTYPE html>
<html lang="en">
  <!--
This file was automatically generated by Strapi.
Any modifications made will be discarded.
-->
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, viewport-fit=cover"
    />
    <meta name="robots" content="noindex" />
    <meta name="referrer" content="same-origin" />
    <title>Strapi Admin</title>
    <style>
      html,
      body,
      #strapi {
        height: 100%;
      }
      body {
        margin: 0;
        -webkit-font-smoothing: antialiased;
      }
    </style>
  </head>
  <body>
    <div id="strapi"></div>
    <noscript
      ><div class="strapi--root">
        <div class="strapi--no-js">
          <style type="text/css">
            .strapi--root {
              position: absolute;
              top: 0;
              right: 0;
              left: 0;
              bottom: 0;
              background: #fff;
            }

            .strapi--no-js {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              text-align: center;
              font-family: helvetica, arial, sans-serif;
            }
          </style>
          <h1>JavaScript disabled</h1>
          <p>
            Please
            <a href="https://www.enable-javascript.com/">enable JavaScript</a>
            in your browser and reload the page to proceed.
          </p>
        </div>
      </div></noscript
    >
  </body>
</html>
</file>

<file path="backend/.vscode/settings.json">
{
  "workbench.colorCustomizations": {
    "activityBar.activeBackground": "#fbed80",
    "activityBar.background": "#fbed80",
    "activityBar.foreground": "#15202b",
    "activityBar.inactiveForeground": "#15202b99",
    "activityBarBadge.background": "#06b9a5",
    "activityBarBadge.foreground": "#15202b",
    "commandCenter.border": "#15202b99",
    "sash.hoverBorder": "#fbed80",
    "statusBar.background": "#f9e64f",
    "statusBar.foreground": "#15202b",
    "statusBarItem.hoverBackground": "#f7df1e",
    "statusBarItem.remoteBackground": "#f9e64f",
    "statusBarItem.remoteForeground": "#15202b",
    "titleBar.activeBackground": "#f9e64f",
    "titleBar.activeForeground": "#15202b",
    "titleBar.inactiveBackground": "#f9e64f99",
    "titleBar.inactiveForeground": "#15202b99"
  },
  "peacock.color": "#f9e64f"
}
</file>

<file path="backend/config/admin.js">
module.exports = ({ env }) => ({
  auth: {
    secret: env('ADMIN_JWT_SECRET'),
  },
  apiToken: {
    salt: env('API_TOKEN_SALT'),
  },
  transfer: {
    token: {
      salt: env('TRANSFER_TOKEN_SALT'),
    }
  }
});
</file>

<file path="backend/config/api.js">
module.exports = {
  rest: {
    defaultLimit: 25,
    maxLimit: 100,
    withCount: true,
  },
};
</file>

<file path="backend/config/database.js">
const path = require('path');

module.exports = ({ env }) => ({
  connection: {
    client: 'sqlite',
    connection: {
      filename: path.join(__dirname, '..', env('DATABASE_FILENAME', '.tmp/data.db')),
    },
    useNullAsDefault: true,
  },
});
</file>

<file path="backend/config/middlewares.js">
module.exports = [
  'strapi::errors',
  'strapi::security',
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
</file>

<file path="backend/config/plugins.js">
module.exports = ({ env }) => ({
  seo: {
    enabled: true,
  },
});
</file>

<file path="backend/config/server.js">
module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS'),
  },
  url: env('STRAPI_URL'),
});
</file>

<file path="backend/public/robots.txt">
# To prevent search engines from seeing the site altogether, uncomment the next two lines:
# User-Agent: *
# Disallow: /
</file>

<file path="backend/src/admin/app.example.js">
const config = {
  locales: [
    // 'ar',
    // 'fr',
    // 'cs',
    // 'de',
    // 'dk',
    // 'es',
    // 'he',
    // 'id',
    // 'it',
    // 'ja',
    // 'ko',
    // 'ms',
    // 'nl',
    // 'no',
    // 'pl',
    // 'pt-BR',
    // 'pt',
    // 'ru',
    // 'sk',
    // 'sv',
    // 'th',
    // 'tr',
    // 'uk',
    // 'vi',
    // 'zh-Hans',
    // 'zh',
  ],
};

const bootstrap = (app) => {
  console.log(app);
};

export default {
  config,
  bootstrap,
};
</file>

<file path="backend/src/admin/webpack.config.example.js">
'use strict';

/* eslint-disable no-unused-vars */
module.exports = (config, webpack) => {
  // Note: we provide webpack above so you should not `require` it
  // Perform customizations to webpack config
  // Important: return the modified config
  return config;
};
</file>

<file path="backend/src/api/article/content-types/article/schema.json">
{
  "kind": "collectionType",
  "collectionName": "articles",
  "info": {
    "singularName": "article",
    "pluralName": "articles",
    "displayName": "Article",
    "description": "Create your blog content"
  },
  "options": {
    "draftAndPublish": true
  },
  "pluginOptions": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "description": {
      "type": "text",
      "maxLength": 256,
      "required": true
    },
    "slug": {
      "type": "uid",
      "targetField": "title"
    },
    "cover": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": [
        "images",
        "files",
        "videos"
      ]
    },
    "category": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::category.category",
      "inversedBy": "articles"
    },
    "blocks": {
      "type": "dynamiczone",
      "components": [
        "shared.media",
        "shared.quote",
        "shared.rich-text",
        "shared.slider",
        "shared.video-embed"
      ]
    },
    "authorsBio": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::author.author",
      "inversedBy": "articles"
    },
    "seo": {
      "type": "component",
      "repeatable": false,
      "component": "shared.seo"
    },
    "premium": {
      "type": "boolean",
      "default": false
    }
  }
}
</file>

<file path="backend/src/api/article/controllers/article.js">
'use strict';

/**
 *  article controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::article.article');
</file>

<file path="backend/src/api/article/middlewares/articles-middleware.js">
"use strict";

/**
 * `articles` middleware
 */

module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    strapi.log.info("In articles middleware.");
    console.log(ctx.state.user);

    // Ensure filters property exists before modifying
    ctx.query.filters = ctx.query.filters || {};

    console.log(ctx.query, "query");

    if (ctx.state.user) ctx.query.filters.premium = [true, false]; // Both premium and non-premium
    else ctx.query.filters.premium = false; // Only non-premium
    
    await next();
  };
};
</file>

<file path="backend/src/api/article/routes/article.js">
"use strict";

/**
 * article router.
 */

const { createCoreRouter } = require("@strapi/strapi").factories;

module.exports = createCoreRouter("api::article.article", {
  config: {
    find: { middlewares: ["api::article.articles-middleware"] },
    findOne: { middlewares: ["api::article.articles-middleware"] },
  },
});
</file>

<file path="backend/src/api/article/services/article.js">
'use strict';

/**
 * article service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::article.article');
</file>

<file path="backend/src/api/author/content-types/author/schema.json">
{
  "kind": "collectionType",
  "collectionName": "authors",
  "info": {
    "singularName": "author",
    "pluralName": "authors",
    "displayName": "Author",
    "description": "Create authors for your content"
  },
  "options": {
    "draftAndPublish": false
  },
  "pluginOptions": {},
  "attributes": {
    "name": {
      "type": "string"
    },
    "avatar": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": [
        "images",
        "files",
        "videos"
      ]
    },
    "email": {
      "type": "string"
    },
    "articles": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::article.article",
      "mappedBy": "authorsBio"
    }
  }
}
</file>

<file path="backend/src/api/author/controllers/author.js">
'use strict';

/**
 *  author controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::author.author');
</file>

<file path="backend/src/api/author/routes/author.js">
'use strict';

/**
 * author router.
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::author.author');
</file>

<file path="backend/src/api/author/services/author.js">
'use strict';

/**
 * author service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::author.author');
</file>

<file path="backend/src/api/category/content-types/category/schema.json">
{
  "kind": "collectionType",
  "collectionName": "categories",
  "info": {
    "singularName": "category",
    "pluralName": "categories",
    "displayName": "Category",
    "description": "Organize your content into categories"
  },
  "options": {
    "draftAndPublish": false
  },
  "pluginOptions": {},
  "attributes": {
    "name": {
      "type": "string"
    },
    "slug": {
      "type": "uid"
    },
    "articles": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::article.article",
      "mappedBy": "category"
    },
    "description": {
      "type": "text"
    }
  }
}
</file>

<file path="backend/src/api/category/controllers/category.js">
'use strict';

/**
 *  category controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::category.category');
</file>

<file path="backend/src/api/category/routes/category.js">
'use strict';

/**
 * category router.
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::category.category');
</file>

<file path="backend/src/api/category/services/category.js">
'use strict';

/**
 * category service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::category.category');
</file>

<file path="backend/src/api/global/content-types/global/schema.json">
{
  "kind": "singleType",
  "collectionName": "globals",
  "info": {
    "singularName": "global",
    "pluralName": "globals",
    "displayName": "Global",
    "name": "global",
    "description": ""
  },
  "options": {
    "increments": true,
    "timestamps": true,
    "draftAndPublish": false
  },
  "pluginOptions": {
    "i18n": {
      "localized": true
    }
  },
  "attributes": {
    "metadata": {
      "type": "component",
      "repeatable": false,
      "component": "meta.metadata",
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "favicon": {
      "type": "media",
      "multiple": false,
      "required": true,
      "allowedTypes": [
        "images"
      ],
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "notificationBanner": {
      "type": "component",
      "repeatable": false,
      "component": "elements.notification-banner",
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "navbar": {
      "type": "component",
      "repeatable": false,
      "component": "layout.navbar",
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "footer": {
      "type": "component",
      "repeatable": false,
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      },
      "component": "layout.footer"
    }
  }
}
</file>

<file path="backend/src/api/global/controllers/global.js">
'use strict';

/**
 *  global controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::global.global');
</file>

<file path="backend/src/api/global/routes/global.js">
'use strict';

/**
 * global router.
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::global.global');
</file>

<file path="backend/src/api/global/services/global.js">
'use strict';

/**
 * global service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::global.global');
</file>

<file path="backend/src/api/lead-form-submission/content-types/lead-form-submission/schema.json">
{
  "kind": "collectionType",
  "collectionName": "lead_form_submissions",
  "info": {
    "singularName": "lead-form-submission",
    "pluralName": "lead-form-submissions",
    "displayName": "Lead form submission",
    "name": "lead-form-submission",
    "description": ""
  },
  "options": {
    "increments": true,
    "timestamps": true,
    "draftAndPublish": false
  },
  "attributes": {
    "email": {
      "type": "string"
    },
    "status": {
      "type": "enumeration",
      "enum": [
        "seen",
        "contacted",
        "ignored"
      ]
    }
  }
}
</file>

<file path="backend/src/api/lead-form-submission/controllers/lead-form-submission.js">
'use strict';

/**
 *  lead-form-submission controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::lead-form-submission.lead-form-submission');
</file>

<file path="backend/src/api/lead-form-submission/routes/lead-form-submission.js">
'use strict';

/**
 * lead-form-submission router.
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::lead-form-submission.lead-form-submission');
</file>

<file path="backend/src/api/lead-form-submission/services/lead-form-submission.js">
'use strict';

/**
 * lead-form-submission service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::lead-form-submission.lead-form-submission');
</file>

<file path="backend/src/api/page/content-types/page/schema.json">
{
  "kind": "collectionType",
  "collectionName": "pages",
  "info": {
    "singularName": "page",
    "pluralName": "pages",
    "displayName": "Page",
    "name": "page",
    "description": ""
  },
  "options": {
    "draftAndPublish": true
  },
  "pluginOptions": {
    "i18n": {
      "localized": true
    }
  },
  "attributes": {
    "shortName": {
      "type": "string",
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "metadata": {
      "type": "component",
      "repeatable": false,
      "component": "meta.metadata",
      "required": true,
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "contentSections": {
      "type": "dynamiczone",
      "components": [
        "sections.hero",
        "sections.bottom-actions",
        "sections.feature-columns-group",
        "sections.feature-rows-group",
        "sections.testimonials-group",
        "sections.large-video",
        "sections.rich-text",
        "sections.pricing",
        "sections.lead-form",
        "sections.features",
        "sections.heading",
        "sections.overview",
        "sections.separator",
        "sections.client-logo",
        "shared.media",
        "sections.large-image"
      ],
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      }
    },
    "slug": {
      "pluginOptions": {
        "i18n": {
          "localized": false
        }
      },
      "type": "string",
      "regex": "^$|^[a-zA-Z/-]+$"
    },
    "heading": {
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      },
      "type": "string"
    },
    "description": {
      "pluginOptions": {
        "i18n": {
          "localized": true
        }
      },
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/api/page/controllers/page.js">
'use strict';

/**
 *  page controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::page.page');
</file>

<file path="backend/src/api/page/middlewares/page-populate-middleware.js">
"use strict";

/**
 * `page-populate-middleware` middleware
 */

const populate = {
  contentSections: {
    populate: {
      picture: {
        fields: ["url", "alternativeText", "caption", "width", "height"],
      },
      buttons: {
        populate: true,
      },
      feature: {
        populate: {
          fields: ["title", "description", "showLink", "newTab", "url", "text"],
          media: {
            fields: ["url", "alternativeText", "caption", "width", "height"],
          },
        },
      },
      testimonials: {
        populate: {
          picture: {
            fields: ["url", "alternativeText", "caption", "width", "height"],
          },
        },
      },
      plans: {
        populate: ["product_features"],
      },
      submitButton: {
        populate: true,
      },
    },
  },
};

module.exports = (config, { strapi }) => {
  // Add your own logic here.
  return async (ctx, next) => {
    ctx.query = {
      populate,
      filters: { slug: ctx.query.filters.slug },
      locale: ctx.query.locale,
    };

    console.log("page-populate-middleware.js: ctx.query = ", ctx.query);

    await next();
  };
};
</file>

<file path="backend/src/api/page/routes/page.js">
'use strict';

/**
 * page router.
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::page.page', {
  config: {
    find: {
      middlewares: ["api::page.page-populate-middleware"]
    },
    findOne: {
      middlewares: ["api::page.page-populate-middleware"]
    },
  }
});
</file>

<file path="backend/src/api/page/services/page.js">
'use strict';

/**
 * page service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::page.page');
</file>

<file path="backend/src/api/product-feature/content-types/product-feature/schema.json">
{
  "kind": "collectionType",
  "collectionName": "product_features",
  "info": {
    "singularName": "product-feature",
    "pluralName": "product-features",
    "displayName": "Product Feature",
    "description": ""
  },
  "options": {
    "draftAndPublish": true
  },
  "pluginOptions": {},
  "attributes": {
    "name": {
      "type": "string",
      "required": true
    }
  }
}
</file>

<file path="backend/src/api/product-feature/controllers/product-feature.js">
'use strict';

/**
 * product-feature controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::product-feature.product-feature');
</file>

<file path="backend/src/api/product-feature/routes/product-feature.js">
'use strict';

/**
 * product-feature router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::product-feature.product-feature');
</file>

<file path="backend/src/api/product-feature/services/product-feature.js">
'use strict';

/**
 * product-feature service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::product-feature.product-feature');
</file>

<file path="backend/src/components/elements/feature-column.json">
{
  "collectionName": "components_slices_feature_columns",
  "info": {
    "name": "FeatureColumn",
    "displayName": "Feature column",
    "icon": "align-center",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "text"
    },
    "icon": {
      "allowedTypes": [
        "images"
      ],
      "type": "media",
      "multiple": false,
      "required": true
    }
  }
}
</file>

<file path="backend/src/components/elements/feature-row.json">
{
  "collectionName": "components_slices_feature_rows",
  "info": {
    "name": "FeatureRow",
    "displayName": "Feature row",
    "icon": "arrows-alt-h",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "text"
    },
    "media": {
      "type": "media",
      "multiple": false,
      "required": true,
      "allowedTypes": [
        "images",
        "videos"
      ]
    },
    "link": {
      "type": "component",
      "repeatable": false,
      "component": "links.link"
    }
  }
}
</file>

<file path="backend/src/components/elements/feature.json">
{
  "collectionName": "components_elements_features",
  "info": {
    "displayName": "Feature",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "description": {
      "type": "text"
    },
    "icon": {
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/components/elements/footer-section.json">
{
  "collectionName": "components_links_footer_sections",
  "info": {
    "name": "FooterSection",
    "displayName": "Footer section",
    "icon": "chevron-circle-down"
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "links": {
      "type": "component",
      "repeatable": true,
      "component": "links.link"
    }
  }
}
</file>

<file path="backend/src/components/elements/logos.json">
{
  "collectionName": "components_elements_logos",
  "info": {
    "name": "logos",
    "displayName": "Logos",
    "icon": "apple-alt"
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "logo": {
      "allowedTypes": [
        "images"
      ],
      "type": "media",
      "multiple": false,
      "required": false
    }
  }
}
</file>

<file path="backend/src/components/elements/notification-banner.json">
{
  "collectionName": "components_elements_notification_banners",
  "info": {
    "name": "NotificationBanner",
    "displayName": "Notification banner",
    "icon": "exclamation",
    "description": ""
  },
  "options": {},
  "attributes": {
    "type": {
      "type": "enumeration",
      "enum": [
        "alert",
        "info",
        "warning"
      ],
      "required": true
    },
    "heading": {
      "type": "string",
      "required": true
    },
    "text": {
      "type": "text",
      "required": true
    },
    "show": {
      "type": "boolean",
      "default": false
    },
    "link": {
      "type": "component",
      "repeatable": false,
      "component": "links.link"
    }
  }
}
</file>

<file path="backend/src/components/elements/plan.json">
{
  "collectionName": "components_elements_plans",
  "info": {
    "name": "plan",
    "displayName": "Pricing plan",
    "icon": "search-dollar",
    "description": ""
  },
  "options": {},
  "attributes": {
    "name": {
      "type": "string"
    },
    "description": {
      "type": "text"
    },
    "isRecommended": {
      "type": "boolean"
    },
    "price": {
      "type": "decimal"
    },
    "pricePeriod": {
      "type": "string"
    },
    "product_features": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::product-feature.product-feature"
    }
  }
}
</file>

<file path="backend/src/components/elements/testimonial.json">
{
  "collectionName": "components_slices_testimonials",
  "info": {
    "name": "Testimonial",
    "displayName": "Testimonial",
    "icon": "user-check",
    "description": ""
  },
  "options": {},
  "attributes": {
    "picture": {
      "type": "media",
      "multiple": false,
      "required": true,
      "allowedTypes": [
        "images"
      ]
    },
    "text": {
      "type": "text",
      "required": true
    },
    "authorName": {
      "type": "string",
      "required": true
    }
  }
}
</file>

<file path="backend/src/components/layout/footer.json">
{
  "collectionName": "components_layout_footers",
  "info": {
    "displayName": "Footer",
    "description": ""
  },
  "options": {},
  "attributes": {
    "footerLogo": {
      "type": "component",
      "repeatable": false,
      "component": "layout.logo"
    },
    "menuLinks": {
      "type": "component",
      "repeatable": true,
      "component": "links.link"
    },
    "legalLinks": {
      "type": "component",
      "repeatable": true,
      "component": "links.link"
    },
    "socialLinks": {
      "type": "component",
      "repeatable": true,
      "component": "links.social-link"
    },
    "categories": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::category.category"
    }
  }
}
</file>

<file path="backend/src/components/layout/logo.json">
{
  "collectionName": "components_layout_logos",
  "info": {
    "displayName": "Logo",
    "description": ""
  },
  "options": {},
  "attributes": {
    "logoImg": {
      "type": "media",
      "multiple": false,
      "required": true,
      "allowedTypes": [
        "images",
        "files",
        "videos",
        "audios"
      ]
    },
    "logoText": {
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/components/layout/navbar.json">
{
  "collectionName": "components_layout_navbars",
  "info": {
    "name": "Navbar",
    "displayName": "Navbar",
    "icon": "map-signs",
    "description": ""
  },
  "options": {},
  "attributes": {
    "links": {
      "type": "component",
      "repeatable": true,
      "component": "links.link"
    },
    "button": {
      "type": "component",
      "repeatable": false,
      "component": "links.button-link"
    },
    "navbarLogo": {
      "type": "component",
      "repeatable": false,
      "component": "layout.logo"
    }
  }
}
</file>

<file path="backend/src/components/links/button-link.json">
{
  "collectionName": "components_links_buttons",
  "info": {
    "name": "Button-link",
    "displayName": "Button link",
    "icon": "fingerprint",
    "description": ""
  },
  "options": {},
  "attributes": {
    "url": {
      "type": "string"
    },
    "newTab": {
      "type": "boolean",
      "default": false
    },
    "text": {
      "type": "string"
    },
    "type": {
      "type": "enumeration",
      "enum": [
        "primary",
        "secondary"
      ]
    }
  }
}
</file>

<file path="backend/src/components/links/button.json">
{
  "collectionName": "components_links_simple_buttons",
  "info": {
    "name": "Button",
    "displayName": "Button",
    "icon": "fingerprint",
    "description": ""
  },
  "options": {},
  "attributes": {
    "text": {
      "type": "string"
    },
    "type": {
      "type": "enumeration",
      "enum": [
        "primary",
        "secondary"
      ]
    }
  }
}
</file>

<file path="backend/src/components/links/link.json">
{
  "collectionName": "components_links_links",
  "info": {
    "name": "Link",
    "displayName": "Link",
    "icon": "link",
    "description": ""
  },
  "options": {},
  "attributes": {
    "url": {
      "type": "string",
      "required": true
    },
    "newTab": {
      "type": "boolean",
      "default": false
    },
    "text": {
      "type": "string",
      "required": true
    }
  }
}
</file>

<file path="backend/src/components/links/social-link.json">
{
  "collectionName": "components_links_social_links",
  "info": {
    "displayName": "Social Link",
    "description": ""
  },
  "options": {},
  "attributes": {
    "url": {
      "type": "string",
      "required": true
    },
    "newTab": {
      "type": "boolean",
      "default": false
    },
    "text": {
      "type": "string",
      "required": true
    },
    "social": {
      "type": "enumeration",
      "enum": [
        "YOUTUBE",
        "TWITTER",
        "DISCORD",
        "WEBSITE"
      ]
    }
  }
}
</file>

<file path="backend/src/components/meta/metadata.json">
{
  "collectionName": "components_meta_metadata",
  "info": {
    "name": "Metadata",
    "displayName": "Metadata",
    "icon": "robot",
    "description": ""
  },
  "options": {},
  "attributes": {
    "metaTitle": {
      "type": "string",
      "required": true
    },
    "metaDescription": {
      "type": "text",
      "required": true
    }
  }
}
</file>

<file path="backend/src/components/sections/bottom-actions.json">
{
  "collectionName": "components_slices_bottom_actions",
  "info": {
    "name": "BottomActions",
    "displayName": "Bottom actions",
    "icon": "angle-double-right",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "buttons": {
      "type": "component",
      "repeatable": true,
      "component": "links.button-link"
    },
    "description": {
      "type": "text"
    }
  }
}
</file>

<file path="backend/src/components/sections/client-logo.json">
{
  "collectionName": "components_sections_client_logos",
  "info": {
    "displayName": "Client Logo",
    "icon": "cube",
    "description": ""
  },
  "options": {},
  "attributes": {
    "picture": {
      "type": "media",
      "multiple": true,
      "required": false,
      "allowedTypes": [
        "images",
        "files",
        "videos",
        "audios"
      ]
    }
  }
}
</file>

<file path="backend/src/components/sections/feature-columns-group.json">
{
  "collectionName": "components_slices_feature_columns_groups",
  "info": {
    "name": "FeatureColumnsGroup",
    "displayName": "Feature columns group",
    "icon": "star-of-life"
  },
  "options": {},
  "attributes": {
    "features": {
      "type": "component",
      "repeatable": true,
      "component": "elements.feature-column"
    }
  }
}
</file>

<file path="backend/src/components/sections/feature-rows-group.json">
{
  "collectionName": "components_slices_feature_rows_groups",
  "info": {
    "name": "FeatureRowsGroup",
    "displayName": "Feaures row group",
    "icon": "bars"
  },
  "options": {},
  "attributes": {
    "features": {
      "type": "component",
      "repeatable": true,
      "component": "elements.feature-row"
    }
  }
}
</file>

<file path="backend/src/components/sections/features.json">
{
  "collectionName": "components_layout_features",
  "info": {
    "displayName": "Features",
    "description": ""
  },
  "options": {},
  "attributes": {
    "heading": {
      "type": "string"
    },
    "description": {
      "type": "text"
    },
    "feature": {
      "displayName": "Feature",
      "type": "component",
      "repeatable": true,
      "component": "elements.feature"
    }
  }
}
</file>

<file path="backend/src/components/sections/heading.json">
{
  "collectionName": "components_sections_headings",
  "info": {
    "displayName": "Heading"
  },
  "options": {},
  "attributes": {
    "heading": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/components/sections/hero.json">
{
  "collectionName": "components_slices_heroes",
  "info": {
    "name": "Hero",
    "displayName": "Hero",
    "icon": "heading",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "string",
      "required": true
    },
    "height": {
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/components/sections/large-video.json">
{
  "collectionName": "components_slices_large_videos",
  "info": {
    "name": "LargeVideo",
    "displayName": "Large video",
    "icon": "play-circle"
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "description": {
      "type": "string"
    },
    "video": {
      "allowedTypes": [
        "videos"
      ],
      "type": "media",
      "multiple": false,
      "required": true
    },
    "poster": {
      "allowedTypes": [
        "images"
      ],
      "type": "media",
      "multiple": false,
      "required": false
    }
  }
}
</file>

<file path="backend/src/components/sections/lead-form.json">
{
  "collectionName": "components_sections_lead_forms",
  "info": {
    "name": "Lead form",
    "displayName": "Lead form",
    "icon": "at",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "emailPlaceholder": {
      "type": "string"
    },
    "submitButton": {
      "type": "component",
      "repeatable": false,
      "component": "links.button"
    },
    "location": {
      "type": "string"
    },
    "description": {
      "type": "text"
    }
  }
}
</file>

<file path="backend/src/components/sections/overview.json">
{
  "collectionName": "components_sections_overviews",
  "info": {
    "displayName": "Overview",
    "icon": "file",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "text",
      "required": true
    },
    "picture": {
      "type": "media",
      "multiple": false,
      "required": true,
      "allowedTypes": [
        "images",
        "files",
        "videos",
        "audios"
      ]
    }
  }
}
</file>

<file path="backend/src/components/sections/pricing.json">
{
  "collectionName": "components_sections_pricings",
  "info": {
    "name": "Pricing",
    "displayName": "Pricing",
    "icon": "dollar-sign"
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "plans": {
      "type": "component",
      "repeatable": true,
      "component": "elements.plan"
    }
  }
}
</file>

<file path="backend/src/components/sections/rich-text.json">
{
  "collectionName": "components_sections_rich_texts",
  "info": {
    "name": "RichText",
    "displayName": "Rich text",
    "icon": "text-height"
  },
  "options": {},
  "attributes": {
    "content": {
      "type": "richtext"
    }
  }
}
</file>

<file path="backend/src/components/sections/separator.json">
{
  "collectionName": "components_sections_separators",
  "info": {
    "displayName": "Separator",
    "icon": "cube"
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/components/sections/testimonials-group.json">
{
  "collectionName": "components_slices_testimonials_groups",
  "info": {
    "name": "TestimonialsGroup",
    "displayName": "Testimonials group",
    "icon": "user-friends",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "description": {
      "type": "text"
    },
    "testimonials": {
      "type": "component",
      "repeatable": true,
      "component": "elements.testimonial"
    }
  }
}
</file>

<file path="backend/src/components/shared/media.json">
{
  "collectionName": "components_shared_media",
  "info": {
    "displayName": "Media",
    "icon": "file-video",
    "description": ""
  },
  "options": {},
  "attributes": {
    "file": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": [
        "images"
      ]
    }
  }
}
</file>

<file path="backend/src/components/shared/quote.json">
{
  "collectionName": "components_shared_quotes",
  "info": {
    "displayName": "Quote",
    "icon": "indent",
    "description": ""
  },
  "options": {},
  "attributes": {
    "title": {
      "type": "string"
    },
    "body": {
      "type": "text",
      "required": true
    },
    "author": {
      "type": "string"
    }
  }
}
</file>

<file path="backend/src/components/shared/rich-text.json">
{
  "collectionName": "components_shared_rich_texts",
  "info": {
    "displayName": "Rich text",
    "icon": "align-justify",
    "description": ""
  },
  "options": {},
  "attributes": {
    "body": {
      "type": "richtext"
    }
  }
}
</file>

<file path="backend/src/components/shared/seo.json">
{
  "collectionName": "components_shared_seos",
  "info": {
    "name": "Seo",
    "icon": "allergies",
    "displayName": "Seo",
    "description": ""
  },
  "options": {},
  "attributes": {
    "metaTitle": {
      "type": "string",
      "required": true
    },
    "metaDescription": {
      "type": "text",
      "required": true
    },
    "shareImage": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": [
        "images"
      ]
    }
  }
}
</file>

<file path="backend/src/components/shared/slider.json">
{
  "collectionName": "components_shared_sliders",
  "info": {
    "displayName": "Slider",
    "icon": "address-book",
    "description": ""
  },
  "options": {},
  "attributes": {
    "files": {
      "type": "media",
      "multiple": true,
      "required": false,
      "allowedTypes": [
        "images"
      ]
    }
  }
}
</file>

<file path="backend/src/components/shared/video-embed.json">
{
  "collectionName": "components_sections_video_embeds",
  "info": {
    "displayName": "Video Embed",
    "description": ""
  },
  "options": {},
  "attributes": {
    "url": {
      "type": "string",
      "required": true
    }
  }
}
</file>

<file path="backend/src/extensions/users-permissions/content-types/user/schema.json">
{
  "kind": "collectionType",
  "collectionName": "up_users",
  "info": {
    "name": "user",
    "description": "",
    "singularName": "user",
    "pluralName": "users",
    "displayName": "User"
  },
  "options": {
    "draftAndPublish": false
  },
  "attributes": {
    "username": {
      "type": "string",
      "minLength": 3,
      "unique": true,
      "configurable": false,
      "required": true
    },
    "email": {
      "type": "email",
      "minLength": 6,
      "configurable": false,
      "required": true
    },
    "provider": {
      "type": "string",
      "configurable": false
    },
    "password": {
      "type": "password",
      "minLength": 6,
      "configurable": false,
      "private": true,
      "searchable": false
    },
    "resetPasswordToken": {
      "type": "string",
      "configurable": false,
      "private": true,
      "searchable": false
    },
    "confirmationToken": {
      "type": "string",
      "configurable": false,
      "private": true,
      "searchable": false
    },
    "confirmed": {
      "type": "boolean",
      "default": false,
      "configurable": false
    },
    "blocked": {
      "type": "boolean",
      "default": false,
      "configurable": false
    },
    "role": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "plugin::users-permissions.role",
      "inversedBy": "users",
      "configurable": false
    },
    "firstName": {
      "type": "string"
    },
    "lastName": {
      "type": "string"
    },
    "bio": {
      "type": "text"
    },
    "image": {
      "allowedTypes": [
        "images",
        "files",
        "videos",
        "audios"
      ],
      "type": "media",
      "multiple": false
    }
  }
}
</file>

<file path="backend/src/index.js">
'use strict';

module.exports = {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/*{ strapi }*/) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap(/*{ strapi }*/) {},
};
</file>

<file path="backend/types/generated/components.d.ts">
import type { Schema, Attribute } from '@strapi/strapi';

export interface ElementsFeatureColumn extends Schema.Component {
  collectionName: 'components_slices_feature_columns';
  info: {
    name: 'FeatureColumn';
    displayName: 'Feature column';
    icon: 'align-center';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text;
    icon: Attribute.Media<'images'> & Attribute.Required;
  };
}

export interface ElementsFeatureRow extends Schema.Component {
  collectionName: 'components_slices_feature_rows';
  info: {
    name: 'FeatureRow';
    displayName: 'Feature row';
    icon: 'arrows-alt-h';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text;
    media: Attribute.Media<'images' | 'videos'> & Attribute.Required;
    link: Attribute.Component<'links.link'>;
  };
}

export interface ElementsFeature extends Schema.Component {
  collectionName: 'components_elements_features';
  info: {
    displayName: 'Feature';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    icon: Attribute.String;
  };
}

export interface ElementsFooterSection extends Schema.Component {
  collectionName: 'components_links_footer_sections';
  info: {
    name: 'FooterSection';
    displayName: 'Footer section';
    icon: 'chevron-circle-down';
  };
  attributes: {
    title: Attribute.String;
    links: Attribute.Component<'links.link', true>;
  };
}

export interface ElementsLogos extends Schema.Component {
  collectionName: 'components_elements_logos';
  info: {
    name: 'logos';
    displayName: 'Logos';
    icon: 'apple-alt';
  };
  attributes: {
    title: Attribute.String;
    logo: Attribute.Media<'images'>;
  };
}

export interface ElementsNotificationBanner extends Schema.Component {
  collectionName: 'components_elements_notification_banners';
  info: {
    name: 'NotificationBanner';
    displayName: 'Notification banner';
    icon: 'exclamation';
    description: '';
  };
  attributes: {
    type: Attribute.Enumeration<['alert', 'info', 'warning']> &
      Attribute.Required;
    heading: Attribute.String & Attribute.Required;
    text: Attribute.Text & Attribute.Required;
    show: Attribute.Boolean & Attribute.DefaultTo<false>;
    link: Attribute.Component<'links.link'>;
  };
}

export interface ElementsPlan extends Schema.Component {
  collectionName: 'components_elements_plans';
  info: {
    name: 'plan';
    displayName: 'Pricing plan';
    icon: 'search-dollar';
    description: '';
  };
  attributes: {
    name: Attribute.String;
    description: Attribute.Text;
    isRecommended: Attribute.Boolean;
    price: Attribute.Decimal;
    pricePeriod: Attribute.String;
    product_features: Attribute.Relation<
      'elements.plan',
      'oneToMany',
      'api::product-feature.product-feature'
    >;
  };
}

export interface ElementsTestimonial extends Schema.Component {
  collectionName: 'components_slices_testimonials';
  info: {
    name: 'Testimonial';
    displayName: 'Testimonial';
    icon: 'user-check';
    description: '';
  };
  attributes: {
    picture: Attribute.Media<'images'> & Attribute.Required;
    text: Attribute.Text & Attribute.Required;
    authorName: Attribute.String & Attribute.Required;
  };
}

export interface LayoutFooter extends Schema.Component {
  collectionName: 'components_layout_footers';
  info: {
    displayName: 'Footer';
    description: '';
  };
  attributes: {
    footerLogo: Attribute.Component<'layout.logo'>;
    menuLinks: Attribute.Component<'links.link', true>;
    legalLinks: Attribute.Component<'links.link', true>;
    socialLinks: Attribute.Component<'links.social-link', true>;
    categories: Attribute.Relation<
      'layout.footer',
      'oneToMany',
      'api::category.category'
    >;
  };
}

export interface LayoutLogo extends Schema.Component {
  collectionName: 'components_layout_logos';
  info: {
    displayName: 'Logo';
    description: '';
  };
  attributes: {
    logoImg: Attribute.Media<'images' | 'files' | 'videos' | 'audios'> &
      Attribute.Required;
    logoText: Attribute.String;
  };
}

export interface LayoutNavbar extends Schema.Component {
  collectionName: 'components_layout_navbars';
  info: {
    name: 'Navbar';
    displayName: 'Navbar';
    icon: 'map-signs';
    description: '';
  };
  attributes: {
    links: Attribute.Component<'links.link', true>;
    button: Attribute.Component<'links.button-link'>;
    navbarLogo: Attribute.Component<'layout.logo'>;
  };
}

export interface LinksButtonLink extends Schema.Component {
  collectionName: 'components_links_buttons';
  info: {
    name: 'Button-link';
    displayName: 'Button link';
    icon: 'fingerprint';
    description: '';
  };
  attributes: {
    url: Attribute.String;
    newTab: Attribute.Boolean & Attribute.DefaultTo<false>;
    text: Attribute.String;
    type: Attribute.Enumeration<['primary', 'secondary']>;
  };
}

export interface LinksButton extends Schema.Component {
  collectionName: 'components_links_simple_buttons';
  info: {
    name: 'Button';
    displayName: 'Button';
    icon: 'fingerprint';
    description: '';
  };
  attributes: {
    text: Attribute.String;
    type: Attribute.Enumeration<['primary', 'secondary']>;
  };
}

export interface LinksLink extends Schema.Component {
  collectionName: 'components_links_links';
  info: {
    name: 'Link';
    displayName: 'Link';
    icon: 'link';
    description: '';
  };
  attributes: {
    url: Attribute.String & Attribute.Required;
    newTab: Attribute.Boolean & Attribute.DefaultTo<false>;
    text: Attribute.String & Attribute.Required;
  };
}

export interface LinksSocialLink extends Schema.Component {
  collectionName: 'components_links_social_links';
  info: {
    displayName: 'Social Link';
    description: '';
  };
  attributes: {
    url: Attribute.String & Attribute.Required;
    newTab: Attribute.Boolean & Attribute.DefaultTo<false>;
    text: Attribute.String & Attribute.Required;
    social: Attribute.Enumeration<['YOUTUBE', 'TWITTER', 'DISCORD', 'WEBSITE']>;
  };
}

export interface MetaMetadata extends Schema.Component {
  collectionName: 'components_meta_metadata';
  info: {
    name: 'Metadata';
    displayName: 'Metadata';
    icon: 'robot';
    description: '';
  };
  attributes: {
    metaTitle: Attribute.String & Attribute.Required;
    metaDescription: Attribute.Text & Attribute.Required;
  };
}

export interface SectionsBottomActions extends Schema.Component {
  collectionName: 'components_slices_bottom_actions';
  info: {
    name: 'BottomActions';
    displayName: 'Bottom actions';
    icon: 'angle-double-right';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    buttons: Attribute.Component<'links.button-link', true>;
    description: Attribute.Text;
  };
}

export interface SectionsClientLogo extends Schema.Component {
  collectionName: 'components_sections_client_logos';
  info: {
    displayName: 'Client Logo';
    icon: 'cube';
    description: '';
  };
  attributes: {
    picture: Attribute.Media<'images' | 'files' | 'videos' | 'audios', true>;
  };
}

export interface SectionsFeatureColumnsGroup extends Schema.Component {
  collectionName: 'components_slices_feature_columns_groups';
  info: {
    name: 'FeatureColumnsGroup';
    displayName: 'Feature columns group';
    icon: 'star-of-life';
  };
  attributes: {
    features: Attribute.Component<'elements.feature-column', true>;
  };
}

export interface SectionsFeatureRowsGroup extends Schema.Component {
  collectionName: 'components_slices_feature_rows_groups';
  info: {
    name: 'FeatureRowsGroup';
    displayName: 'Feaures row group';
    icon: 'bars';
  };
  attributes: {
    features: Attribute.Component<'elements.feature-row', true>;
  };
}

export interface SectionsFeatures extends Schema.Component {
  collectionName: 'components_layout_features';
  info: {
    displayName: 'Features';
    description: '';
  };
  attributes: {
    heading: Attribute.String;
    description: Attribute.Text;
    feature: Attribute.Component<'elements.feature', true>;
  };
}

export interface SectionsHeading extends Schema.Component {
  collectionName: 'components_sections_headings';
  info: {
    displayName: 'Heading';
  };
  attributes: {
    heading: Attribute.String & Attribute.Required;
    description: Attribute.String;
  };
}

export interface SectionsHero extends Schema.Component {
  collectionName: 'components_slices_heroes';
  info: {
    name: 'Hero';
    displayName: 'Hero';
    icon: 'heading';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    height: Attribute.String;
  };
}

export interface SectionsLargeImage extends Schema.Component {
  collectionName: 'components_sections_large_images';
  info: {
    displayName: 'Large image';
    icon: 'cube';
    description: '';
  };
  attributes: {
    picture: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface SectionsLargeVideo extends Schema.Component {
  collectionName: 'components_slices_large_videos';
  info: {
    name: 'LargeVideo';
    displayName: 'Large video';
    icon: 'play-circle';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.String;
    video: Attribute.Media<'videos'> & Attribute.Required;
    poster: Attribute.Media<'images'>;
  };
}

export interface SectionsLeadForm extends Schema.Component {
  collectionName: 'components_sections_lead_forms';
  info: {
    name: 'Lead form';
    displayName: 'Lead form';
    icon: 'at';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    emailPlaceholder: Attribute.String;
    submitButton: Attribute.Component<'links.button'>;
    location: Attribute.String;
    description: Attribute.Text;
  };
}

export interface SectionsOverview extends Schema.Component {
  collectionName: 'components_sections_overviews';
  info: {
    displayName: 'Overview';
    icon: 'file';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text & Attribute.Required;
    picture: Attribute.Media<'images' | 'files' | 'videos' | 'audios'> &
      Attribute.Required;
  };
}

export interface SectionsPricing extends Schema.Component {
  collectionName: 'components_sections_pricings';
  info: {
    name: 'Pricing';
    displayName: 'Pricing';
    icon: 'dollar-sign';
  };
  attributes: {
    title: Attribute.String;
    plans: Attribute.Component<'elements.plan', true>;
  };
}

export interface SectionsRichText extends Schema.Component {
  collectionName: 'components_sections_rich_texts';
  info: {
    name: 'RichText';
    displayName: 'Rich text';
    icon: 'text-height';
  };
  attributes: {
    content: Attribute.RichText;
  };
}

export interface SectionsSeparator extends Schema.Component {
  collectionName: 'components_sections_separators';
  info: {
    displayName: 'Separator';
    icon: 'cube';
  };
  attributes: {
    title: Attribute.String;
  };
}

export interface SectionsTestimonialsGroup extends Schema.Component {
  collectionName: 'components_slices_testimonials_groups';
  info: {
    name: 'TestimonialsGroup';
    displayName: 'Testimonials group';
    icon: 'user-friends';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    testimonials: Attribute.Component<'elements.testimonial', true>;
  };
}

export interface SharedMedia extends Schema.Component {
  collectionName: 'components_shared_media';
  info: {
    displayName: 'Media';
    icon: 'file-video';
    description: '';
  };
  attributes: {
    file: Attribute.Media<'images'>;
  };
}

export interface SharedQuote extends Schema.Component {
  collectionName: 'components_shared_quotes';
  info: {
    displayName: 'Quote';
    icon: 'indent';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    body: Attribute.Text & Attribute.Required;
    author: Attribute.String;
  };
}

export interface SharedRichText extends Schema.Component {
  collectionName: 'components_shared_rich_texts';
  info: {
    displayName: 'Rich text';
    icon: 'align-justify';
    description: '';
  };
  attributes: {
    body: Attribute.RichText;
  };
}

export interface SharedSeo extends Schema.Component {
  collectionName: 'components_shared_seos';
  info: {
    name: 'Seo';
    icon: 'allergies';
    displayName: 'Seo';
    description: '';
  };
  attributes: {
    metaTitle: Attribute.String & Attribute.Required;
    metaDescription: Attribute.Text & Attribute.Required;
    shareImage: Attribute.Media<'images'>;
  };
}

export interface SharedSlider extends Schema.Component {
  collectionName: 'components_shared_sliders';
  info: {
    displayName: 'Slider';
    icon: 'address-book';
    description: '';
  };
  attributes: {
    files: Attribute.Media<'images', true>;
  };
}

export interface SharedVideoEmbed extends Schema.Component {
  collectionName: 'components_sections_video_embeds';
  info: {
    displayName: 'Video Embed';
    description: '';
  };
  attributes: {
    url: Attribute.String & Attribute.Required;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'elements.feature-column': ElementsFeatureColumn;
      'elements.feature-row': ElementsFeatureRow;
      'elements.feature': ElementsFeature;
      'elements.footer-section': ElementsFooterSection;
      'elements.logos': ElementsLogos;
      'elements.notification-banner': ElementsNotificationBanner;
      'elements.plan': ElementsPlan;
      'elements.testimonial': ElementsTestimonial;
      'layout.footer': LayoutFooter;
      'layout.logo': LayoutLogo;
      'layout.navbar': LayoutNavbar;
      'links.button-link': LinksButtonLink;
      'links.button': LinksButton;
      'links.link': LinksLink;
      'links.social-link': LinksSocialLink;
      'meta.metadata': MetaMetadata;
      'sections.bottom-actions': SectionsBottomActions;
      'sections.client-logo': SectionsClientLogo;
      'sections.feature-columns-group': SectionsFeatureColumnsGroup;
      'sections.feature-rows-group': SectionsFeatureRowsGroup;
      'sections.features': SectionsFeatures;
      'sections.heading': SectionsHeading;
      'sections.hero': SectionsHero;
      'sections.large-image': SectionsLargeImage;
      'sections.large-video': SectionsLargeVideo;
      'sections.lead-form': SectionsLeadForm;
      'sections.overview': SectionsOverview;
      'sections.pricing': SectionsPricing;
      'sections.rich-text': SectionsRichText;
      'sections.separator': SectionsSeparator;
      'sections.testimonials-group': SectionsTestimonialsGroup;
      'shared.media': SharedMedia;
      'shared.quote': SharedQuote;
      'shared.rich-text': SharedRichText;
      'shared.seo': SharedSeo;
      'shared.slider': SharedSlider;
      'shared.video-embed': SharedVideoEmbed;
    }
  }
}
</file>

<file path="backend/types/generated/contentTypes.d.ts">
import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    singularName: 'release';
    pluralName: 'releases';
    displayName: 'Release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    timezone: Attribute.String;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    singularName: 'release-action';
    pluralName: 'release-actions';
    displayName: 'Release Action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    contentType: Attribute.String & Attribute.Required;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    isEntryValid: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 50;
        },
        number
      >;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    firstName: Attribute.String;
    lastName: Attribute.String;
    bio: Attribute.Text;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiArticleArticle extends Schema.CollectionType {
  collectionName: 'articles';
  info: {
    singularName: 'article';
    pluralName: 'articles';
    displayName: 'Article';
    description: 'Create your blog content';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 256;
      }>;
    slug: Attribute.UID<'api::article.article', 'title'>;
    cover: Attribute.Media<'images' | 'files' | 'videos'>;
    category: Attribute.Relation<
      'api::article.article',
      'manyToOne',
      'api::category.category'
    >;
    blocks: Attribute.DynamicZone<
      [
        'shared.media',
        'shared.quote',
        'shared.rich-text',
        'shared.slider',
        'shared.video-embed'
      ]
    >;
    authorsBio: Attribute.Relation<
      'api::article.article',
      'manyToOne',
      'api::author.author'
    >;
    seo: Attribute.Component<'shared.seo'>;
    premium: Attribute.Boolean & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::article.article',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::article.article',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAuthorAuthor extends Schema.CollectionType {
  collectionName: 'authors';
  info: {
    singularName: 'author';
    pluralName: 'authors';
    displayName: 'Author';
    description: 'Create authors for your content';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String;
    avatar: Attribute.Media<'images' | 'files' | 'videos'>;
    email: Attribute.String;
    articles: Attribute.Relation<
      'api::author.author',
      'oneToMany',
      'api::article.article'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::author.author',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::author.author',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCategoryCategory extends Schema.CollectionType {
  collectionName: 'categories';
  info: {
    singularName: 'category';
    pluralName: 'categories';
    displayName: 'Category';
    description: 'Organize your content into categories';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String;
    slug: Attribute.UID;
    articles: Attribute.Relation<
      'api::category.category',
      'oneToMany',
      'api::article.article'
    >;
    description: Attribute.Text;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::category.category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::category.category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGlobalGlobal extends Schema.SingleType {
  collectionName: 'globals';
  info: {
    singularName: 'global';
    pluralName: 'globals';
    displayName: 'Global';
    name: 'global';
    description: '';
  };
  options: {
    increments: true;
    timestamps: true;
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    metadata: Attribute.Component<'meta.metadata'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    favicon: Attribute.Media<'images'> &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    notificationBanner: Attribute.Component<'elements.notification-banner'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    navbar: Attribute.Component<'layout.navbar'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    footer: Attribute.Component<'layout.footer'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global.global',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::global.global',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::global.global',
      'oneToMany',
      'api::global.global'
    >;
    locale: Attribute.String;
  };
}

export interface ApiLeadFormSubmissionLeadFormSubmission
  extends Schema.CollectionType {
  collectionName: 'lead_form_submissions';
  info: {
    singularName: 'lead-form-submission';
    pluralName: 'lead-form-submissions';
    displayName: 'Lead form submission';
    name: 'lead-form-submission';
    description: '';
  };
  options: {
    increments: true;
    timestamps: true;
    draftAndPublish: false;
  };
  attributes: {
    email: Attribute.String;
    status: Attribute.Enumeration<['seen', 'contacted', 'ignored']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::lead-form-submission.lead-form-submission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::lead-form-submission.lead-form-submission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPagePage extends Schema.CollectionType {
  collectionName: 'pages';
  info: {
    singularName: 'page';
    pluralName: 'pages';
    displayName: 'Page';
    name: 'page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    shortName: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    metadata: Attribute.Component<'meta.metadata'> &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    contentSections: Attribute.DynamicZone<
      [
        'sections.hero',
        'sections.bottom-actions',
        'sections.feature-columns-group',
        'sections.feature-rows-group',
        'sections.testimonials-group',
        'sections.large-video',
        'sections.rich-text',
        'sections.pricing',
        'sections.lead-form',
        'sections.features',
        'sections.heading',
        'sections.overview',
        'sections.separator',
        'sections.client-logo',
        'shared.media',
        'sections.large-image'
      ]
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    slug: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    heading: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    description: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::page.page', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::page.page', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::page.page',
      'oneToMany',
      'api::page.page'
    >;
    locale: Attribute.String;
  };
}

export interface ApiProductFeatureProductFeature extends Schema.CollectionType {
  collectionName: 'product_features';
  info: {
    singularName: 'product-feature';
    pluralName: 'product-features';
    displayName: 'Product Feature';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-feature.product-feature',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-feature.product-feature',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'api::article.article': ApiArticleArticle;
      'api::author.author': ApiAuthorAuthor;
      'api::category.category': ApiCategoryCategory;
      'api::global.global': ApiGlobalGlobal;
      'api::lead-form-submission.lead-form-submission': ApiLeadFormSubmissionLeadFormSubmission;
      'api::page.page': ApiPagePage;
      'api::product-feature.product-feature': ApiProductFeatureProductFeature;
    }
  }
}
</file>

<file path="backend/.editorconfig">
root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[{package.json,*.yml}]
indent_style = space
indent_size = 2

[*.md]
trim_trailing_whitespace = false
</file>

<file path="backend/.env.example">
HOST=0.0.0.0
PORT=1337
APP_KEYS=1HwdEoqcHeMAHHOKd2GT3w==,cJgut5x+p9pLReHVCohb6Q==,5GeGETVsDi2mHRfFwFTVtA==,gD9aqP2m077UrO1AsXb6zQ==
API_TOKEN_SALT=UWQBFkVprokBKe4HmwPfwQ==
ADMIN_JWT_SECRET=wsby4XurQaMjNqKUzAU+Og==
TRANSFER_TOKEN_SALT=ksAOnzkZB9foRgzlBtyFEA==

# Database
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db
JWT_SECRET=KA8LxmxSi/vwrVyIoM9ZJg==
</file>

<file path="backend/.eslintignore">
.cache
build
**/node_modules/**
</file>

<file path="backend/.eslintrc">
{
  "parser": "babel-eslint",
  "extends": "eslint:recommended",
  "env": {
    "commonjs": true,
    "es6": true,
    "node": true,
    "browser": false
  },
  "parserOptions": {
    "ecmaFeatures": {
      "experimentalObjectRestSpread": true,
      "jsx": false
    },
    "sourceType": "module"
  },
  "globals": {
    "strapi": true
  },
  "rules": {
    "indent": ["error", 2, { "SwitchCase": 1 }],
    "linebreak-style": ["error", "unix"],
    "no-console": 0,
    "quotes": ["error", "single"],
    "semi": ["error", "always"]
  }
}
</file>

<file path="backend/.gitignore">
############################
# OS X
############################

.DS_Store
.AppleDouble
.LSOverride
Icon
.Spotlight-V100
.Trashes
._*


############################
# Linux
############################

*~


############################
# Windows
############################

Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp


############################
# Packages
############################

*.7z
*.csv
*.dat
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
*.com
*.class
*.dll
*.exe
*.o
*.seed
*.so
*.swo
*.swp
*.swn
*.swm
*.out
*.pid


############################
# Logs and databases
############################

.tmp
*.log
*.sql
*.sqlite
*.sqlite3


############################
# Misc.
############################

*#
ssl
.idea
nbproject
public/uploads/*
!public/uploads/.gitkeep

############################
# Node.js
############################

lib-cov
lcov.info
pids
logs
results
node_modules
.node_history

############################
# Tests
############################

testApp
coverage

############################
# Strapi
############################

.env
license.txt
exports
*.cache
dist
build
.strapi-updater.json
</file>

<file path="backend/package.json">
{
  "name": "corporate-blog-template",
  "private": true,
  "version": "0.1.0",
  "description": "A Strapi application",
  "scripts": {
    "develop": "strapi develop",
    "start": "strapi start",
    "build": "strapi build",
    "strapi": "strapi"
  },  "dependencies": {
    "@strapi/plugin-cloud": "4.24.5",
    "@strapi/plugin-i18n": "4.24.5",
    "@strapi/plugin-seo": "^1.9.8",
    "@strapi/plugin-users-permissions": "4.24.5",
    "@strapi/strapi": "4.24.5",
    "better-sqlite3": "8.6.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "react-router-dom": "5.3.4",
    "styled-components": "5.3.3"
  },
  "author": {
    "name": "A Strapi developer"
  },
  "strapi": {
    "uuid": "42b937aa-0f16-4a30-bb55-95f60ec03b48"
  },
  "engines": {
    "node": ">=16.x.x <=20.x.x",
    "npm": ">=6.0.0"
  },
  "license": "MIT"
}
</file>

<file path="backend/README.md">
# 🚀 Getting started with Strapi

Strapi comes with a full featured [Command Line Interface](https://docs.strapi.io/developer-docs/latest/developer-resources/cli/CLI.html) (CLI) which lets you scaffold and manage your project in seconds.

### `develop`

Start your Strapi application with autoReload enabled. [Learn more](https://docs.strapi.io/developer-docs/latest/developer-resources/cli/CLI.html#strapi-develop)

```
npm run develop
# or
yarn develop
```

### `start`

Start your Strapi application with autoReload disabled. [Learn more](https://docs.strapi.io/developer-docs/latest/developer-resources/cli/CLI.html#strapi-start)

```
npm run start
# or
yarn start
```

### `build`

Build your admin panel. [Learn more](https://docs.strapi.io/developer-docs/latest/developer-resources/cli/CLI.html#strapi-build)

```
npm run build
# or
yarn build
```

## ⚙️ Deployment

Strapi gives you many possible deployment options for your project. Find the one that suits you on the [deployment section of the documentation](https://docs.strapi.io/developer-docs/latest/setup-deployment-guides/deployment.html).

## 📚 Learn more

- [Resource center](https://strapi.io/resource-center) - Strapi resource center.
- [Strapi documentation](https://docs.strapi.io) - Official Strapi documentation.
- [Strapi tutorials](https://strapi.io/tutorials) - List of tutorials made by the core team and the community.
- [Strapi blog](https://docs.strapi.io) - Official Strapi blog containing articles made by the Strapi team and the community.
- [Changelog](https://strapi.io/changelog) - Find out about the Strapi product updates, new features and general improvements.

Feel free to check out the [Strapi GitHub repository](https://github.com/strapi/strapi). Your feedback and contributions are welcome!

## ✨ Community

- [Discord](https://discord.strapi.io) - Come chat with the Strapi community including the core team.
- [Forum](https://forum.strapi.io/) - Place to discuss, ask questions and find answers, show your Strapi project and get feedback or just talk with other Community members.
- [Awesome Strapi](https://github.com/strapi/awesome-strapi) - A curated list of awesome things related to Strapi.

---

<sub>🤫 Psst! [Strapi is hiring](https://strapi.io/careers).</sub>
</file>

<file path="frontend/app/api/auth/login.server.ts">
import { createUserSession } from "~/utils/session.server";

export async function login(redirectTo: string, data: any) {
  const baseUrl = process.env.STRAPI_API_URL;
  const query = `/api/auth/local`;
  const request = await fetch(baseUrl + query, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const response = await request.json();
  if (response.error) return { error: response.error };
  return await createUserSession(redirectTo, {
    user: response.user,
    jwt: response.jwt,
  });
}
</file>

<file path="frontend/app/api/auth/register.server.ts">
import { createUserSession } from "~/utils/session.server";

export async function register(redirectTo: string, data: any) {

  const baseUrl = process.env.STRAPI_API_URL
  const query = `/api/auth/local/register`;
  const request = await fetch(baseUrl + query, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const response = await request.json();
  if (response.error) return { error: response.error };
  return await createUserSession(redirectTo, { user: response.user, jwt: response.jwt });
}
</file>

<file path="frontend/app/api/auth/userme.server.ts">
import { getUserData } from "~/utils/session.server";

import qs from "qs";

const query = qs.stringify({
  fields: ["firstName", "lastName",  "username", "email", "bio"],
  populate: {
    image: {
      fields: ["url", "alternativeText"],
    },

  },
});

export async function userme(request: Request) {
  const user = await getUserData(request);
  if (!user) return null;

  const baseUrl = process.env.STRAPI_API_URL || "http://127.0.0.1:1337";
  const path = `/api/users/me?${query}`;

  try {
    const userRequest = await fetch(baseUrl + path, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        authorization: `Bearer ${user.jwt}`,
      },
    });

    const userData = await userRequest.json();
    return userData;
  } catch (error) {
    console.error(error);
    throw new Error("Error fetching user data");
  }
}
</file>

<file path="frontend/app/api/delete-image.server.ts">
export async function deleteImage(imageId: string, jwt: string) {
  const baseUrl = process.env.STRAPI_API_URL;
  const path = "/api/upload/files/" + imageId;

  const url = baseUrl + path;
  
  const checkIfImageExists = await fetch(url + imageId);

  if (checkIfImageExists.status === 403) return;

  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${jwt}`,
    },
  });

  const data = await response.json();
  return data;
}
</file>

<file path="frontend/app/api/fetch-strapi-data.server.ts">
import qs from "qs";
import { getStrapiURL } from "~/utils/api-helpers";

export async function fetchStrapiData(
  path: string,
  urlParamsObject: Record<string, any> = {},
  jwt?: string | null
) {
  
  const headers: { [key: string]: string } = {
    "Content-Type": "application/json"
  };

  // Add Authorization header only if jwt is provided.
  if (jwt) headers["Authorization"] = `Bearer ${jwt}`;

  const queryString = qs.stringify(urlParamsObject);
  const requestUrl = `${getStrapiURL(`/api${path}`)}${queryString ? `?${queryString}` : ""}`;

  const response = await fetch(requestUrl, { headers: headers });

  if (!response.ok) {
    const errorMessage = await response.text();
    console.error(errorMessage || response.statusText);
    throw new Error(errorMessage || "An error occurred. Please try again.");
  }

  const data = await response.json();
  return data;
}
</file>

<file path="frontend/app/api/form.server.ts">
export async function submitJoinForm(formData: {
  data: {
    email: string;
  } | null;
}): Promise<any> {
  const URL =  process.env.STRAPI_API_URL + "/api/lead-form-submissions";
  const SUBMIT_FORM_STRAP_KEY = process.env.SUBMIT_FORM_STRAPI_KEY;

  try {
    const data = await fetch(URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${SUBMIT_FORM_STRAP_KEY}`,
      },
      body: JSON.stringify(formData),
    });
    return data;
  } catch (error) {
    console.error(error, "error");
  }
}
</file>

<file path="frontend/app/api/update-user-profile.server.ts">
export async function updateProfile(
  data: any,
  imageId: string,
  userId: string,
  jwt: string
) {
  const baseUrl = process.env.STRAPI_API_URL;
  const query = `/api/users/${userId}`;

  const url = baseUrl + query;
  const formData = new FormData();

  for (const key in data) {
    if (Object.hasOwn(data, key)) {
      formData.append(key, data[key]);
    }
  }

  if (imageId) formData.append("image", imageId);

  const response = await fetch(url, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${jwt}`,
    },
    body: formData,
  });

  const dataResponse = await response.json();
  return dataResponse;
}
</file>

<file path="frontend/app/api/upload-image.server.ts">
export async function uploadImage(
  image: any,
  jwt: string
) {
  const baseUrl = process.env.STRAPI_API_URL;
  const path = "/api/upload";

  const url = baseUrl + path;
  const formData = new FormData();

  formData.append("files", image, image.name);

  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${jwt}`,
    },
    body: formData,
  });

  const dataResponse = await response.json();
  return dataResponse;
}
</file>

<file path="frontend/app/components/ArticleSelect.tsx">
import { Link }from "@remix-run/react";

interface Category {
  id: number;
  attributes: {
    name: string;
    slug: string;
    articles: {
      data: Array<{}>;
    };
  };
}

interface Article {
  id: number;
  attributes: {
    title: string;
    slug: string;
  };
}

function selectedFilter(current: string, selected: string) {
  return current === selected
    ? "px-3 py-1 rounded-lg hover:underline bg-white text-eguard"
    : "px-3 py-1 rounded-lg hover:underline bg-white text-gray-900";
}

export default function ArticleSelect({
  categories,
  articles,
  params,
}: {
  categories: Category[];
  articles: Article[];
  params: {
    slug: string;
    category: string;
  };
}) {

  return (
    <div className="p-4 rounded-lg bg-slate-50 min-h-[365px] relative">
      <h4 className="text-xl font-semibold text-eguard">Browse By Category</h4>

      <div>
        <div className="flex flex-wrap py-6 space-x-2 border-eguard">
          {categories.map((category: Category) => {
            if (category.attributes.articles.data.length === 0) return null;
            return (
              <Link
                key={category.id}
                to={`/blog/${category.attributes.slug}`}
                prefetch="intent"
                className={selectedFilter(
                  category.attributes.slug,
                  params.category
                )}
              >
                #{category.attributes.name}
              </Link>
            );
          })}
          <Link to={"/blog"} className={selectedFilter("", "filter")} prefetch="intent">
            #all
          </Link>
        </div>

        <div className="space-y-2">
          <h4 className="text-lg font-semibold text-eguard">Other Posts You May Like</h4>
          <ul className="ml-4 space-y-1 list-disc">
            {articles.map((article: Article) => {
              return (
                <li key={article.id} className="text-black">
                  <Link
                    rel="noopener noreferrer"
                    to={`/blog/${params.category}/${article.attributes.slug}`}
                    prefetch="intent"
                    className={`${
                      params.slug === article.attributes.slug &&
                      "text--400"
                    }  hover:underline hover:text--400 transition-colors duration-200`}
                  >
                    {article.attributes.title}
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
}
</file>

<file path="frontend/app/components/Avatar.tsx">
import type { StrapiUserResponse } from "~/types";
import { getStrapiMedia } from "~/utils/api-helpers";
import { Link } from "@remix-run/react";

export const Avatar: React.FC<{ user: StrapiUserResponse }> = ({ user }) => {
  const imageUrl =
    getStrapiMedia(user.image?.url) ||
    "https://robohash.org/<EMAIL>";

  return (
    <div className="flex mt-auto items-center justify-between">
      <div className="flex items-center">
        <img
          className="h-8 w-8 mr-3 rounded-full"
          src={imageUrl}
          alt={user?.image?.alternativeText || "User Avatar"}
        />
        <h5 className="leading-none font-semibold text-gray-100 hover:text-eguard">
          <Link to="profile">{user.username}</Link>
        </h5>
      </div>
    </div>
  );
};
</file>

<file path="frontend/app/components/Banner.tsx">
import classNames from "classnames";

function colors(type: string) {
  switch (type) {
    case "info":
      return "bg-violet-400";
    case "warning":
      return "bg-yellow-500";
    case "alert":
      return "bg-pink-500";
    default:
      return "bg-gray-900";
  }
}

interface BannerProps {
  data: {
    heading: string;
    text: string;
    type: string;
    link: {
      id: number;
      url: string;
      newTab: boolean;
      text: string;
    };
  } | null;
}

export default function Banner({ data }: BannerProps) {
  if (!data) return null;
  const { heading, text, type, link } = data;
  return (
    <div className="pointer-events-none fixed inset-x-0 bottom-0 sm:flex sm:justify-center sm:px-6 sm:pb-5 lg:px-8">
      <div
        className={classNames(
          "pointer-events-auto flex items-center justify-between gap-x-6 py-2.5 px-6 sm:rounded-xl sm:py-3 sm:pr-3.5 sm:pl-4",
          colors(type)
        )}
      >
        <p className="text-sm leading-6 text-white">
          <a href={link.url} target={link.newTab ? "_blank" : "_self"}>
            <strong className="font-semibold">{heading}</strong> {text}&nbsp;
            <span aria-hidden="true">&rarr;</span>
          </a>
        </p>
      </div>
    </div>
  );
}
</file>

<file path="frontend/app/components/BlogList.tsx">
import { Link } from "@remix-run/react";
import { getStrapiMedia, formatDate } from "~/utils/api-helpers";

interface Article {
  id: 4;
  attributes: {
    title: string;
    description: string;
    slug: string;
    premium: boolean;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    cover: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    category: {
      data: {
        attributes: {
          name: string;
          slug: string;
        };
      };
    };
    authorsBio: {
      data: {
        attributes: {
          name: string;
          avatar: {
            data: {
              attributes: {
                url: string;
              };
            };
          };
        };
      };
    };
  };
}

export default function BlogList({
  data: articles,
  children,
}: {
  readonly data: Article[];
  readonly children?: React.ReactNode;
}) {
  return (
    <section className="min-h-screen p-6 mx-auto space-y-6 sm:space-y-12 bg-white">
      <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
        {articles.map((article) => {
          const imageUrl = getStrapiMedia(
            article.attributes.cover.data?.attributes.url
          );

          const category = article.attributes.category.data?.attributes;
          const authorsBio = article.attributes.authorsBio.data?.attributes;

          const avatarUrl = getStrapiMedia(
            authorsBio?.avatar?.data?.attributes.url ||
              "https://robohash.org/<EMAIL>"
          );

          return (
            <Link
              to={`/news/${category?.slug}/${article.attributes.slug}`}
              key={article.id}
              prefetch="intent"
              className="max-w-sm mx-auto group hover:no-underline focus:no-underline bg-slate-50 lg:w-[300px] xl:min-w-[375px] rounded-2xl overflow-hidden shadow-lg"
            >
              {imageUrl && (
                <div className="relative">
                  <img
                    alt="presentation"
                    className="object-cover w-full h-44 "
                    src={imageUrl}
                  />
                  {article.attributes.premium && (
                    <div className="absolute top-0 right-0 bg-red-600 text-white px-2 py-1 font-bold uppercase">
                      Premium
                    </div>
                  )}
                </div>
              )}
              <div className="p-6 space-y-2 relative">

                <h3 className="text-2xl text-eguard font-semibold group-hover:underline group-focus:underline">
                  {article.attributes.title}
                </h3>

                <div className="flex justify-between items-center">
                  <span className="text-xs text-black">
                    {formatDate(article.attributes.publishedAt)}
                  </span>
                  
                </div>
                <p className="py-4 text-gray">
                  {article.attributes.description}
                </p>
              </div>
            </Link>
          );
        })}
      </div>
      {children && children}
    </section>
  );
}
</file>

<file path="frontend/app/components/ClientLogo.tsx">
import { getStrapiMedia } from "../utils/api-helpers";

interface ClientLogoProps {
    data?: {
        id: number
        __component: string
        picture: {
            data: Array<{
                id: number
                attributes: {
                    url: string
                    alternativeText: string | null
                    caption: string | null
                    width: number
                    height: number
                }
            }>
        }
    }
}

export default function ClientLogo({ data }: ClientLogoProps) {
    const logos = data?.picture?.data || [];

    return (
        <div className="bg-white">
            <div className="container mx-auto flex flex-wrap justify-center items-center gap-4 md:gap-8 py-8 md:py-16">
                {logos.map((logo) => (
                <div key={logo.id} className="flex items-center justify-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6 px-4">
                    <img
                        src={getStrapiMedia(logo.attributes.url) || ""}
                        alt={logo.attributes.alternativeText || "Client logo"}
                        width={logo.attributes.width}
                        height={logo.attributes.height}
                        className="object-contain w-full h-auto max-h-16 md:max-h-24"
                    />
                </div>
            ))}
        </div>
        </div>
    );
}
</file>

<file path="frontend/app/components/Email.tsx">
import FormSubmit from "~/routes/api.join-form";

import {
  isRouteErrorResponse,
  useRouteError,
} from "@remix-run/react";

export function ErrorBoundary() {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div>
        <h1>hey</h1>
      </div>
    );
  } else if (error instanceof Error) {
    return (
      <div>
        <h2>hey</h2>
      </div>
    );
  } else {
    return <h1>Unknown Error</h1>;
  }
}


interface EmailProps {
  id: string;
  __component: string;
  title: string;
  description: string;
  emailPlaceholder: string;
  submitButton: {
    text: string;
  };
}

export default function Email({ data }: { data: EmailProps }) {

  return (
    <section className="py-6 bg-white text-black">
      <div className="container mx-auto flex flex-col justify-center p-4 space-y-8 md:p-10 lg:space-y-0 lg:space-x-12 lg:justify-between lg:flex-row">
        <div className="flex flex-col space-y-4 text-center lg:text-left">
          <h1 className="text-5xl font-bold leading-none">{data.title}</h1>
          <p className="text-lg">{data.description}</p>
        </div>
        <FormSubmit placeholder={data.emailPlaceholder} text={data.submitButton.text} />
        </div>
    </section>
  );
}
</file>

<file path="frontend/app/components/Error.tsx">
export default function Error() {
  return (
    <div className="container mx-auto p-8">
      <h2>Something went wrong!</h2>
    </div>
  );
}
</file>

<file path="frontend/app/components/Features.tsx">
import { Link } from "@remix-run/react";
import { 
  Zap, 
  Star, 
  Heart,
  ShieldCheck,
  CircleCheckBig,
  TabletSmartphone,
  View,
  MessageCircleWarning,
  Lock,
  Blocks,
  ShieldUser,
  ServerCog,
  // Add other icons you need
  type LucideIcon 
} from 'lucide-react';

interface FeaturesProps {
  data: {
    heading: string;
    description: string;
    feature: FeatureResponse[];
  };
}

interface FeatureProps {
  id: string;
  title: string;
  description: string;
  showLink: boolean;
  newTab: boolean;
  url: string;
  text: string;
  icon: string;
}

interface FeatureResponse {
  id: string;
  title: string;
  description: string;
  showLink: boolean;
  newTab: boolean;
  url: string;
  text: string;
  icon: string;
}

// Create an icon map
const IconMap: Record<string, LucideIcon> = {
  Zap,
  Star,
  Heart,
  ShieldCheck,
  CircleCheckBig,
  TabletSmartphone,
  View,
  MessageCircleWarning,
  Lock,
  Blocks,
  ShieldUser,
  ServerCog,
  // Add other icons you plan to use
};

function Feature({
  title,
  description,
  showLink,
  newTab,
  url,
  text,
  icon,
}: FeatureResponse) {
  const Icon = IconMap[icon];

  return (
    <div className="flex flex-col items-center p-4">
      {Icon && (
        <Icon className="w-8 h-8 text-eguard" />
      )}
      <h3 className="my-3 text-3xl font-semibold text-center">{title}</h3>
      <div className="space-y-1 leading-tight my-6 text-justify">
        <p>{description}</p>
      </div>
      {showLink && url && text && (
        <div>
          <Link
            to={url}
            target={newTab ? "_blank" : "_self"}
            className="inline-block px-4 py-2 mt-4 text-sm font-semibold text-white transition duration-200 ease-in-out bg-violet-500 rounded-lg hover:bg-violet-600"
          >
            {text}
          </Link>
        </div>
      )}
    </div>
  );
}

export default function Features({ data }: FeaturesProps) {
  return (
    <section className="bg-white  m:py-12 lg:py-12">
      {/* <div className="container mx-auto py-4 space-y-2 text-center">
        <h2 className="text-5xl font-bold text-center">{data.heading}</h2>
        <p className="text-gray-400">{data.description}</p>
      </div> */}
      <div className="container mx-auto my-6 grid justify-center gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {data.feature.map((feature: FeatureProps, index: number) => (
          <Feature key={index} {...feature} />
        ))}
      </div>
    </section>
  );
}
</file>

<file path="frontend/app/components/Footer.tsx">
interface FooterProps {
  className?: string;
  logoUrl: string;
}

export default function Footer({ className = '', logoUrl }: FooterProps) {
  return (
    <footer className={`bg-footer ${className}`} style={{ background: 'linear-gradient(to right, #D7E5F4, #FEFEFE)' }}>
      <p className="text-gray-800">Copyright © Eguard {new Date().getFullYear()}</p>
    </footer>
  );
}
</file>

<file path="frontend/app/components/GradientHero.tsx">
import HighlightedText from "./HighlightedText"

interface HeroProps {
    data?: {
      id: string
      title: string
      description: string
      height?: string // New height prop
    }
  }
  
  export default function GradientHero({ data}: HeroProps) {

    console.log(data);
    // Default values if data is not provided
    const title = data?.title || "Default Title"
    const description = data?.description || "Default description text"
    
    return (
      <section
        className="w-full flex items-center justify-center pt-20"
        style={{
          background: "linear-gradient(90deg, #D7E5F4, #FEFEFE)",
          minHeight: data?.height ?? '50vh',
        }}
      >
        <div 
          className="container px-4 mx-auto text-center"
          style={{ paddingTop: data?.height, paddingBottom: data?.height }}
        >
          {/* <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 mb-4">{title}</h1> */}
          {/* <p className="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto">{description}</p> */}
          <HighlightedText
            text={title}
            tag="h1"
            className="text-5xl font-bold leading-none sm:text-6xl mb-8"
            color="text-eguard"
          />

          <HighlightedText
            text={description}
            tag="p"
            className="tmt-6 mb-8 text-lg sm:mb-12"
            color="text-eguard"
          />
        </div>
      </section>
    )
  }
</file>

<file path="frontend/app/components/Hero.tsx">
import { Link } from "@remix-run/react";
import HighlightedText from "./HighlightedText";
import { getStrapiMedia } from "~/utils/api-helpers";
import { renderButtonStyle } from "~/utils/render-button-style";

interface Button {
  id: string;
  url: string;
  text: string;
  type: string;
  newTab: boolean;
}

interface Picture {
  data: {
    id: string;
    attributes: {
      url: string;
      name: string;
      alternativeText: string;
    };
  };
}

interface HeroProps {
  data: {
    id: string;
    title: string;
    description: string;
    picture: Picture;
    buttons: Button[];
  };
}

export default function Hero({ data }: HeroProps) {
  const imgUrl = getStrapiMedia(data.picture.data.attributes.url);
  return (
    <section className="bg-white text-gray-100">
      <div className="container flex flex-col justify-center p-6 mx-auto sm:py-12 lg:py-24 lg:flex-row lg:justify-between">
        <div className="flex flex-col justify-center p-6 text-center rounded-lg lg:max-w-md xl:max-w-lg lg:text-left">
          <HighlightedText
            text={data.title}
            tag="h1"
            className="text-5xl font-bold leading-none sm:text-6xl mb-8"
            color="text-violet-400"
          />

          <HighlightedText
            text={data.description}
            tag="p"
            className="tmt-6 mb-8 text-lg sm:mb-12"
            color="text-violet-400"
          />
          <div className="flex flex-col space-y-4 sm:items-center sm:justify-center sm:flex-row sm:space-y-0 sm:space-x-4 lg:justify-start">
            {data.buttons.map((button: Button, index: number) => (
              <Link
                key={index}
                to={button.url}
                target={button.newTab ? "_blank" : "_self"}
                className={renderButtonStyle(button.type)}
              >
                {button.text}
              </Link>
            ))}
          </div>
        </div>
        <div className="flex items-center justify-center p-6 mt-8 lg:mt-0 h-72 sm:h-80 lg:h-96 xl:h-112 2xl:h-128">
          <img
            src={imgUrl || ""}
            alt={
              data.picture.data.attributes.alternativeText || "none provided"
            }
            className="object-contain h-72 sm:h-80 lg:h-96 xl:h-112 2xl:h-128 "
          />
        </div>
      </div>
    </section>
  );
}
</file>

<file path="frontend/app/components/HighlightedText.tsx">
interface HighlightedTextProps {
  text: string;
  tag: string;
  className?: string;
  color?: string;
}

export default function HighlightedText({
  text,
  tag,
  className,
  color,
}: HighlightedTextProps) {
  const tempText = text.split(" ");
  let result = [];

  result.push(`<${tag} class="${className ? className : ""}">`);

  tempText.forEach((word: string, index: number) => {
    if (word.includes("[")) {
      const highlight = word.replace("[", "").replace("]", "");
      result.push(
        `<span key=${index} class="${color ? color : ""}">${highlight}</span> `
      );
    } else result.push(word + " ");
  });

  result.push(`</${tag}>`);

  return <div dangerouslySetInnerHTML={{ __html: result.join("") }} />;
}
</file>

<file path="frontend/app/components/ImageField.tsx">
interface ImageFieldProps {
  readonly name: string;
  readonly onFileChange: (file: File | undefined) => void;
  readonly previewImage: string | null;
  readonly onPreviewImageChange: (image: string | null) => void;
  readonly existingPreviewUrl?: string;
}

export function ImageField({
  name,
  onFileChange,
  previewImage,
  onPreviewImageChange,
  existingPreviewUrl,
}: ImageFieldProps) {
  const FILE_SIZE_LIMIT = 2 * 1024 * 1024; // 2MB
  const SUPPORTED_FILE_TYPES = ["image/jpeg", "image/png"];

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];

    // If no file is selected, set file to undefined
    if (!selectedFile) {
      onFileChange(undefined);
      onPreviewImageChange(null);
      return;
    }

    // File Size Validation
    if (selectedFile.size > FILE_SIZE_LIMIT) {
      alert("File size exceeds the 2MB limit.");
      return;
    }

    // File Type Validation
    if (!SUPPORTED_FILE_TYPES.includes(selectedFile.type)) {
      alert("Unsupported file type. Please upload a JPEG or PNG image.");
      return;
    }

    onFileChange(selectedFile);

    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target && event.target.result) {
        onPreviewImageChange(event.target.result as string);
      }
    };
    reader.readAsDataURL(selectedFile);
  };

  const displayedPreview = previewImage || existingPreviewUrl;

  return (
    <div className="flex flex-col">
      <input name={name} type="file" onChange={handleImageChange} />
      {displayedPreview && (
        <div>
          <img
            src={displayedPreview}
            alt="Profile Preview"
            className="rounded-lg mt-4"
          />
        </div>
      )}
    </div>
  );
}
</file>

<file path="frontend/app/components/ImageSlider.tsx">
"use client";
import { Fade } from "react-slideshow-image";
import { getStrapiMedia } from "../utils/api-helpers";

interface Image {
  id: number;
  attributes: {
    alternativeText: string | null;
    caption: string | null;
    url: string;
  };
}

interface SlidShowProps {
  files: {
    data: Image[];
  };
}

export default function Slideshow({ data }: { data: SlidShowProps }) {
  return (
    <div className="slide-container">
      <Fade>
        {data.files.data.map((fadeImage: Image, index) => {
          const imageUrl = getStrapiMedia(fadeImage.attributes.url);
          return (
            <div key={index}>
              {imageUrl && <img className="w-full h-96 object-cover rounded-lg" alt="alt text" src={imageUrl} />}
            </div>
          );
        })}
      </Fade>
    </div>
  );
}
</file>

<file path="frontend/app/components/Loader.tsx">
export default function Loader() {
  return (
    <div className="absolute inset-0 flex items-center justify-center z-50  bg-opacity-40 bg-gray-500">
    <div role="status">
      <svg
        aria-hidden="true"
        className="inline w-8 h-8 mr-2 text-gray-200 animate-spin text-gray-600 fill-purple-400"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
      <span className="sr-only">Loading...</span>
    </div>
  </div>
  )
}
</file>

<file path="frontend/app/components/LoginForm.tsx">
import { Form, Link } from "@remix-run/react";
import { SubmitButton } from "~/components/SubmitButton";

export const LoginForm: React.FC<{
  data: { message: string };
}> = ({ data }) => {
  return (
    <div className="my-6 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md w-96">
        <h2 className="text-2xl font-bold mb-4">Login</h2>

        <Form method="post">
          <div className="mb-4">
            <label
              className="block text-sm font-medium text-gray-700"
              htmlFor="identifier"
            >
              Email or Username
            </label>
            <input
              type="text"
              id="identifier"
              name="identifier"
              className="mt-1 p-2 w-full border rounded-md text-gray-950"
              autoComplete="username"
            />
          </div>

          <div className="mb-4">
            <label
              className="block text-sm font-medium text-gray-700"
              htmlFor="password"
            >
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="mt-1 p-2 w-full border rounded-md text-gray-950"
              autoComplete="current-password"
            />
          </div>
          <SubmitButton text="Login" />
          <Link
            to="/register"
            className="my-2 block text-gray-600 hover:underline"
          >
            Don't have account.
          </Link>
          {data && (
            <div>
              <p aria-live="polite" className="sr-only" role="status">
                {data.message}
              </p>
              <p className="flex justify-center items-center text-purple-700 p-2">
                {data.message}
              </p>
            </div>
          )}
        </Form>
      </div>
    </div>
  );
};
</file>

<file path="frontend/app/components/Logo.tsx">
import React from "react";
import { Link } from "@remix-run/react";

export default function Logo({
  src,
  children,
}: {
  src: string | null;
  children?: React.ReactNode;
}) {
  return (
    <Link
      to="/"
      aria-label="Back to homepage"
      className="flex items-center p-2"
    >
      {src && <img src={src} alt="logo" width={45} height={45} />}
      <div className="ml-2">{children}</div>
    </Link>
  );
}
</file>

<file path="frontend/app/components/Media.tsx">
import { getStrapiMedia } from "../utils/api-helpers";

interface MediaProps {
  file: {
    data: {
      id: string;
      attributes: {
        url: string;
        name: string;
        alternativeText: string;
      };
    };
  };
}

export default function Media({ data }: { data: MediaProps }) {
  const imgUrl = getStrapiMedia(data.file.data.attributes.url);
  return (
    <div className="flex items-center justify-center mt-8 lg:mt-0 h-72 sm:h-80 lg:h-96 xl:h-112 2xl:h-128">
      <img
        src={imgUrl || ""}
        alt={data.file.data.attributes.alternativeText || "none provided"}
        className="object-cover w-full h-full rounded-lg overflow-hidden"
      />
    </div>
  );
}
</file>

<file path="frontend/app/components/Navbar.tsx">
import Logo from "./Logo";
import type { StrapiUserResponse } from "~/types";
import { Link, useLocation } from "@remix-run/react";
import { Avatar } from "~/components/Avatar";
import { Logout } from "~/routes/logout";
import { useState } from 'react';

interface NavLinkData {
  id: number;
  url: string;
  newTab: boolean;
  text: string;
}

function isRouteMatch(url: string, pathname: string) {
  return pathname === url;
}

function NavLink({ url, text }: NavLinkData) {
  const { pathname } = useLocation();
  const isActive = isRouteMatch(url, pathname);

  return (

    <>
      <li className={`nav-item mt-5 ${isActive ? 'active' : ''}`}>
        <Link
          to={url}
          className="nav-link"
        >
          <div className="relative">
            <div className={`z-50 relative text-center ${isRouteMatch(url, pathname) ? 'text-black' : 'text-gray-100'}`}>
              <strong>{text}</strong>
            </div>
            {/* <img 
                src="/nav.svg" 
                className="absolute z-40 top-2 " 
                style={{ display: isRouteMatch(url, pathname) ? 'block' : 'none' }}
            /> */}
          </div>
        </Link>
      </li>

    </>

  );
}

export default function Navbar({
  links,
  logoUrl,
  logoText,
  user,
}: {
  links: Array<NavLinkData>;
  logoUrl: string | null;
  logoText: string | null;
  user: StrapiUserResponse | null;
}) {
  return (
    <div className="navbar bg-eguard z-50">
      <div className="container flex h-16 mx-auto px-0 sm:px-6 items-center justify-between relative">
        {/* Logo di kiri */}
        <div className="flex items-center">
          <Logo src={logoUrl}>
            {logoText && <h2 className="text-2xl font-bold">{logoText}</h2>}
          </Logo>
        </div>

        {/* NavLink di tengah */}
        <div className="absolute left-1/2 -translate-x-1/2 hidden lg:flex">

          <ul className="nav-list">
            {links.map((item: NavLinkData) => (
              <NavLink key={item.id} {...item} />
            ))}
          </ul>
        </div>

        {/* Tombol menu untuk mobile */}
        <button className="p-4 lg:hidden">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6 text-gray-100"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>

      <style>{`
        .navbar {
          position: fixed;
          left: 0;
          top: 0;
          width: 100%;
        }
        .nav-list {
          display: flex;
          list-style: none;
          margin: 0;
          padding-left: 40px;
        }
        .nav-item {
          padding: 10px 30px;
          font-size: 1.2rem;
          border-radius: 20px 20px 0 0;
          position: relative;
        }
        // .nav-item.active {
        //   background: #fff;
        // }
     
      `}</style>
    </div>
  );
}
</file>

<file path="frontend/app/components/Navbar2.tsx">
import Logo from "./Logo";
import type { StrapiUserResponse } from "~/types";
import { Link, useLocation } from "@remix-run/react";
import { Avatar } from "~/components/Avatar";
import { Logout } from "~/routes/logout";
import { useState } from 'react';

interface NavLinkData {
  id: number;
  url: string;
  newTab: boolean;
  text: string;
}

function isRouteMatch(url: string, pathname: string) {
  return pathname === url;
}

function NavLink({ url, text }: NavLinkData) {
  const { pathname } = useLocation();
  const isActive = isRouteMatch(url, pathname);
  
  return (
    <li className={`nav-item mt-5 ${isActive ? 'active' : ''}`}>
      <Link
        to={url}
        className="nav-link"
      >
        {text}
      </Link>
    </li>
  );
}

export default function Navbar({
  links,
  logoUrl,
  logoText,
  user,
}: {
  links: Array<NavLinkData>;
  logoUrl: string | null;
  logoText: string | null;
  user: StrapiUserResponse | null;
}) {
  return (
    <div className="navbar bg-eguard">
      <div className="container flex h-16 mx-auto px-0 sm:px-6 items-center justify-between relative">
        {/* Logo di kiri */}
        <div className="flex items-center">
          <Logo src={logoUrl}>
            {logoText && <h2 className="text-2xl font-bold">{logoText}</h2>}
          </Logo>
        </div>

        {/* NavLink di tengah */}
        <div className="absolute left-1/2 -translate-x-1/2 hidden lg:flex">
          <ul className="nav-list">
            {links.map((item: NavLinkData) => (
              <NavLink key={item.id} {...item} />
            ))}
          </ul>
        </div>

        {/* Tombol menu untuk mobile */}
        <button className="p-4 lg:hidden">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6 text-gray-100"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>

      <style>{`
        .navbar {
          position: fixed;
          left: 0;
          top: 0;
          width: 100%;
        }
        .nav-list {
          display: flex;
          list-style: none;
          margin: 0;
          padding-left: 40px;
        }
        .nav-item {
          padding: 10px 30px;
          font-size: 1rem;
          border-radius: 20px 20px 0 0;
          position: relative;
        }
        .nav-item.active {
          background: #fff;
        }
        .nav-link {
          text-decoration: none;
          color: #fff;
        }
        .nav-item.active .nav-link {
          color: #177DFA;
        }
        .nav-item.active .nav-link::before {
          content: "";
          left: -30px;
          bottom: 0;
          height: 30px;
          width: 30px;
          position: absolute;
          background: #177DFA;
          border-radius: 50%;
          box-shadow: 15px 15px 0 transparent;
        }
        .nav-item.active .nav-link::after {
          content: "";
          right: -30px;
          bottom: 0;
          height: 30px;
          width: 30px;
          position: absolute;
          background: #177DFA;
          border-radius: 50%;
          box-shadow: -15px 15px 0 transparent;
        }
      `}</style>
    </div>
  );
}
</file>

<file path="frontend/app/components/Overview.tsx">
import { getStrapiMedia } from "../utils/api-helpers";

interface OverviewProps {
    data?: {
        id: string
        title: string
        description: string
        picture: {
            data: {
                id: string
                attributes: {
                    name: string
                    alternativeText: string
                    url: string
                }
            }
        }
    }
}

export default function Overview({ data }: OverviewProps) {

    const title = data?.title || "Default Title"
    const description = data?.description || "Default description text"
    const imageUrl = getStrapiMedia(data?.picture?.data?.attributes?.url || "");

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 relative p-4 bg-white">
            <div className="relative px-5">
                <div className="overflow-hidden">
                    <img 
                        src={imageUrl || ""} 
                        alt={data?.picture?.data?.attributes?.alternativeText} 
                        className="w-full object-cover -top-24 rounded-lg py-10" 
                    />
                </div>
            </div>
            <div className="flex flex-col justify-center py-12 px-10">
                <h2 className="text-center text-2xl font-bold mb-4">{title}</h2>
                <p className="text-justify">{description}</p>
            </div>
        </div>
    )
}
</file>

<file path="frontend/app/components/PageHeader.tsx">
import React from "react";

interface PageHeaderProps {
  heading: string,
  text?: string,
}

export default function PageHeader({ heading, text } : PageHeaderProps) {
  return (
    <div className="my-16 w-full text-center">
    { text && <span className="text-violet-400 font-bold ">{text}</span> }
    <h2 className="text-4xl my-4 lg:text-5xl text-white font-bold font-heading uppercase">{heading}</h2>
  </div>
  );
}
</file>

<file path="frontend/app/components/Post.tsx">
import { formatDate, getStrapiMedia } from "~/utils/api-helpers";
import { postRenderer } from "~/utils/post-renderer";

interface Article {
  id: number;
  attributes: {
    title: string;
    description: string;
    slug: string;
    cover: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    authorsBio: {
      data: {
        attributes: {
          name: string;
          avatar: {
            data: {
              attributes: {
                url: string;
              };
            };
          };
        };
      };
    };
    blocks: any[];
    publishedAt: string;
  };
}

export default function Post({ data }: { data: Article }) {
  const { title, description, publishedAt, cover, authorsBio } =
  data.attributes;
  const author = authorsBio.data?.attributes;
  const imageUrl = getStrapiMedia(cover.data?.attributes.url);
  const authorImgUrl = getStrapiMedia(
    authorsBio.data?.attributes.avatar.data.attributes.url
  );

  return (
    <article className="space-y-8 text-gray-50">
      {imageUrl && (
        <img
          src={imageUrl}
          alt="article cover"
          className="w-full h-96 object-cover rounded-lg"
        />
      )}
      <div className="space-y-6">
        <h1 className="leading-tight text-5xl font-bold text-black">{title}</h1>
        
      </div>

      <div className="text-gray-100">
        <p>{description}</p>

        {data.attributes.blocks.map((section: any, index: number) =>
          postRenderer(section, index)
        )}
      </div>
    </article>
  );
}
</file>

<file path="frontend/app/components/Pricing.tsx">
interface Feature {
  id: string;
  attributes: {
    name: string;
  };
}

interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  pricePeriod: string;
  isRecommended: boolean;
  product_features: {
    data: Feature[];
  };
}

interface PriceProps {
  data: {
    id: string;
    title: string;
    plans: Plan[];
  };
}

export default function Pricing({ data }: PriceProps) {
  return (
    <section className="py-20 bg-gray-900 text-gray-100 m:py-12 lg:py-24">
      <div className="container px-4 mx-auto ">
        <div className="max-w-2xl mx-auto mb-16 text-center">
          <span className="font-bold tracking-wider uppercase text-violet-400">
            Pricing
          </span>
          <h2 className="text-4xl font-bold lg:text-5xl">{data.title}</h2>
        </div>
        <div className="flex flex-wrap items-stretch max-w-5xl mx-auto">
          {data.plans.map((plan: Plan) => (
            <div
              key={plan.id}
              className="w-full p-4 mb-8  sm:mx-40 lg:mx-0 lg:w-1/3 lg:mb-0"
            >
              <div
                className={`flex flex-col p-6 space-y-6 rounded shadow sm:p-8 min-h-[475px] min-w-[300px] ${
                  plan.isRecommended ? "bg-violet-600" : "bg-gray-800"
                }`}
              >
                <div className="space-y-2">
                  <h4 className="text-3xl font-bold mb-6">{plan.name}</h4>
                  <span className="text-6xl font-bold ">
                    {plan.price}
                    <span
                      className={`ml-1 text-sm tracking-wid ${
                        plan.isRecommended
                          ? "text-gray-900"
                          : "text-violet-500"
                      }`}
                    >
                      {plan.pricePeriod.toLowerCase()}
                    </span>
                  </span>
                </div>
                <p
                  className={`mt-3 leading-relaxed text-lg font-bold ${
                    plan.isRecommended
                      ? "text-gray-900"
                      : "text-gray-400"
                  }`}
                >
                  {plan.description}
                </p>
                <ul
                  className={`flex-1 mb-6 ${
                    plan.isRecommended
                      ? "text-gray-900 font-semibold"
                      : "text-gray-400"
                  }`}
                >
                  {plan.product_features.data.map((feature: Feature) => (
                    <li key={feature.id} className="flex mb-2 space-x-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        className={`flex-shrink-0 w-6 h-6 ${
                          plan.isRecommended
                            ? "text-gray-900"
                            : "text-gray-400"
                        }`}
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      <span>{feature.attributes.name}</span>
                    </li>
                  ))}
                </ul>
                <button
                  type="button"
                  className={`inline-block px-5 py-3 font-semibold tracking-wider text-center rounded   ${
                    plan.isRecommended
                      ? "bg-gray-900 text-violet-400"
                      : "bg-violet-400 text-gray-900"
                  }`}
                >
                  Get Started
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
</file>

<file path="frontend/app/components/Quote.tsx">
interface QuoteProps {
  data: {
    title: string;
    body: string;
    author: string;
  };
}

export default function Quote({ data }: QuoteProps) {
  const { title, body, author } = data;

  return (
    <div className="flex flex-col items-center mx-12 lg:mx-0 py-44">
      {title && <h2 className="my-4">{title}</h2>}
      <div className="relative text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          fill="currentColor"
          className="absolute top-0 -left-4 w-4 h-4 text-gray-700"
        >
          <path d="M232,246.857V16H16V416H54.4ZM48,48H200V233.143L48,377.905Z"></path>
          <path d="M280,416h38.4L496,246.857V16H280ZM312,48H464V233.143L312,377.905Z"></path>
        </svg>
        <p className="px-6 py-1 text-lg italic">{body}</p>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          fill="currentColor"
          className="absolute bottom-0 -right-4 w-4 h-4 text-gray-700"
        >
          <path d="M280,185.143V416H496V16H457.6ZM464,384H312V198.857L464,54.1Z"></path>
          <path d="M232,16H193.6L16,185.143V416H232ZM200,384H48V198.857L200,54.1Z"></path>
        </svg>
      </div>
      <span className="w-12 h-1 my-2 rounded-lg bg-violet-400"></span>
      {author ? <p>{author}</p> : "unknown"}
    </div>
  );
}
</file>

<file path="frontend/app/components/RegisterForm.tsx">
import { Form, Link } from "@remix-run/react";
import { SubmitButton } from "~/components/SubmitButton";

export const RegisterForm: React.FC<{
  data: { message: string };
}> = ({ data }) => {
  return (
    <div className="my-6 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md w-96">
        <h2 className="text-2xl font-bold mb-4">Register</h2>
        <Form method="post">
          <div className="mb-4">
            <label
              className="block text-sm font-medium text-gray-700"
              htmlFor="username"
            >
              Username
            </label>
            <input
              type="text"
              id="username"
              name="username"
              className="mt-1 p-2 w-full border rounded-md text-gray-950"
              autoComplete="username"
            />
          </div>

          <div className="mb-4">
            <label
              className="block text-sm font-medium text-gray-700"
              htmlFor="email"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              className="mt-1 p-2 w-full border rounded-md text-gray-950"
              autoComplete="email"
            />
          </div>

          <div className="mb-4">
            <label
              className="block text-sm font-medium text-gray-700"
              htmlFor="password"
            >
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="mt-1 p-2 w-full border rounded-md text-gray-950"
              autoComplete="new-password"
            />
          </div>
          <SubmitButton text="Register" />
          <Link
            to="/login"
            className="my-2 block text-gray-600 hover:underline"
          >
            Have account.
          </Link>
          {data && (
            <div>
              <p aria-live="polite" className="sr-only" role="status">
                {data.message}
              </p>
              <p className="flex justify-center items-center text-purple-700 p-2">
                {data.message}
              </p>
            </div>
          )}
        </Form>
      </div>
    </div>
  );
};
</file>

<file path="frontend/app/components/RichText.tsx">
import * as React from "react";
import pkg from '@markdoc/markdoc';
const { renderers, parse, transform } = pkg;

// TODO: FIGURE OUT TYPES FOR MARKDOC

export function markdown(markdown: any, config: any) {
  return transform(parse(markdown, config));
}

export default function Markdown({
  data,
  config = {},
}: {
  data: {
    body: string;
  };
  config?: any;
}) {
  return (
    <div className="rich-text py-6 bg-white text-black ">
      {renderers.react(markdown(data.body, config), React)}
    </div>
  );
}
</file>

<file path="frontend/app/components/Separator.tsx">
interface SeparatorProps {
    data?: {
        className?: string;
        title: string;
    }
}

export default function Separator({ data }: SeparatorProps) {
    const className = data?.className || "";
    const title = data?.title || "";

    return (
        <div className={`relative w-full bg-[#1b232c] ${className}`}>
            <svg id="wave" style={{ transform: 'rotate(0deg)', transition: '0.3s' }} viewBox="0 0 1440 100" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="sw-gradient-0" x1="0" x2="0" y1="1" y2="0">
                        <stop stopColor="rgba(0, 0, 0, 1)" offset="0%" />
                        <stop stopColor="rgba(22, 30, 38, 1)" offset="100%" />
                    </linearGradient>
                </defs>
                <path style={{ transform: 'translate(0, 0px)', opacity: 1 }} fill="url(#sw-gradient-0)" d="M0,54L34.3,54C68.6,54,137,54,206,63C274.3,72,343,90,411,96C480,102,549,96,617,90C685.7,84,754,78,823,69C891.4,60,960,48,1029,63C1097.1,78,1166,120,1234,123C1302.9,126,1371,90,1440,84C1508.6,78,1577,102,1646,102C1714.3,102,1783,78,1851,63C1920,48,1989,42,2057,36C2125.7,30,2194,24,2263,42C2331.4,60,2400,102,2469,126C2537.1,150,2606,156,2674,132C2742.9,108,2811,54,2880,27C2948.6,0,3017,0,3086,27C3154.3,54,3223,108,3291,117C3360,126,3429,90,3497,90C3565.7,90,3634,126,3703,138C3771.4,150,3840,138,3909,114C3977.1,90,4046,54,4114,60C4182.9,66,4251,114,4320,126C4388.6,138,4457,114,4526,108C4594.3,102,4663,114,4731,117C4800,120,4869,114,4903,111L4937.1,108L4937.1,180L4902.9,180C4868.6,180,4800,180,4731,180C4662.9,180,4594,180,4526,180C4457.1,180,4389,180,4320,180C4251.4,180,4183,180,4114,180C4045.7,180,3977,180,3909,180C3840,180,3771,180,3703,180C3634.3,180,3566,180,3497,180C3428.6,180,3360,180,3291,180C3222.9,180,3154,180,3086,180C3017.1,180,2949,180,2880,180C2811.4,180,2743,180,2674,180C2605.7,180,2537,180,2469,180C2400,180,2331,180,2263,180C2194.3,180,2126,180,2057,180C1988.6,180,1920,180,1851,180C1782.9,180,1714,180,1646,180C1577.1,180,1509,180,1440,180C1371.4,180,1303,180,1234,180C1165.7,180,1097,180,1029,180C960,180,891,180,823,180C754.3,180,686,180,617,180C548.6,180,480,180,411,180C342.9,180,274,180,206,180C137.1,180,69,180,34,180L0,180Z" />
            </svg>

            {/* Centered Title */}
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white text-5xl font-semibold">{title}</span>
            </div>
        </div>
    );
}
</file>

<file path="frontend/app/components/SubmitButton.tsx">
export const SubmitButton: React.FC<{text: string }> = ({ text }) => {
  return (
    <button
      type="submit"
      aria-disabled={false}
      className="w-full px-8 py-3 text-lg font-semibold rounded bg-violet-400 text-gray-900"
    >
      {text}
    </button>
  );
};
</file>

<file path="frontend/app/components/Testimonials.tsx">
import { getStrapiMedia } from "../utils/api-helpers";

interface TestimonialResponse {
  text: string;
  authorName: string;
  picture: {
    data: {
      id: string;
      attributes: {
        name: string;
        alternativeText: string;
        url: string;
      };
    };
  };
}

interface TestimonialsProps {
  data: {
    id: string;
    title: string;
    description: string;
    testimonials: TestimonialResponse[];
  };
}

function Testimonial({ text, authorName, picture }: TestimonialResponse) {
  const imageUrl = getStrapiMedia(picture.data.attributes.url);
  return (
    <div className="flex flex-col items-center mx-12 lg:mx-0">
      <div className="flex items-center">
        <div className="my-6">
          <img
            src={imageUrl || ""}
            alt={picture.data.attributes.alternativeText || "none provided"}
            className="inline-block h-32 w-32 rounded-full object-cover"
          />
        </div>
      </div>
      <div className="relative text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          fill="currentColor"
          className="absolute top-0 left-0 w-8 h-8 text-gray-700"
        >
          <path d="M232,246.857V16H16V416H54.4ZM48,48H200V233.143L48,377.905Z"></path>
          <path d="M280,416h38.4L496,246.857V16H280ZM312,48H464V233.143L312,377.905Z"></path>
        </svg>
        <p className="px-6 py-1 text-lg italic">{text}</p>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          fill="currentColor"
          className="absolute bottom-0 right-0 w-8 h-8 text-gray-700"
        >
          <path d="M280,185.143V416H496V16H457.6ZM464,384H312V198.857L464,54.1Z"></path>
          <path d="M232,16H193.6L16,185.143V416H232ZM200,384H48V198.857L200,54.1Z"></path>
        </svg>
      </div>
      <span className="w-12 h-1 my-2 rounded-lg bg-violet-400"></span>
      <p>{authorName}</p>
    </div>
  );
}

export default function Testimonials({ data }: TestimonialsProps) {
  return (
    <section className="bg-gray-900 text-gray-100  m:py-12 lg:py-24">
      <div className="container mx-auto py-4 space-y-2 text-center">
        <h1 className="text-4xl font-semibold leading-none text-center">
          {data.title}
        </h1>
        <p className="mt-4 text-lg text-center">{data.description}</p>
      </div>
      <div className="container mx-auto grid grid-cols-1 gap-8 lg:gap-20 md:px-10 md:pb-10 lg:grid-cols-2">
        {data.testimonials.map((testimonial: TestimonialResponse, index: number) => (
          <Testimonial key={index} {...testimonial} />
        ))}
      </div>
    </section>
  );
}
</file>

<file path="frontend/app/components/UserProfileForm.tsx">
import { useState } from "react";
import { Form, useNavigation } from "@remix-run/react";
import { ImageField } from "~/components/ImageField";
import { getStrapiMedia } from "~/utils/api-helpers";

export interface UserDataProps {
  user: {
    id: number;
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    bio: string;
    image: Image;
  };
}

export interface Image {
  id: number;
  url: string;
  alternativeText: null;
}

const message = null;

export default function UserProfileForm({ data }: { data: UserDataProps }) {
  const navigation = useNavigation();
  const isLoading = navigation.state === "loading" ? "loading" : "";

  const fullImageUrl = data.user?.image
    ? getStrapiMedia(data.user?.image?.url)
    : "https://picsum.photos/200";

  const [file, setFile] = useState<File>();
  const [previewImage, setPreviewImage] = useState<string | null>(
    fullImageUrl || null
  );

  return (
    <div className="my-6 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md w-3/4">
        <h2 className="text-2xl font-bold mb-4">User Profile</h2>
        <Form method="POST" encType="multipart/form-data">
          <fieldset
            disabled={isLoading === "loading"}
            className="grid grid-cols-2 gap-4"
          >
            {/* First row */}
            <div className="mb-4 col-span-1">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor="username"
              >
                Username
              </label>
              <input
                type="text"
                id="username"
                name="username"
                className="mt-1 p-2 w-full border rounded-md text-gray-950"
                defaultValue={data.user.username}
                disabled
              />
            </div>

            <div className="mb-4 col-span-1">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor="email"
              >
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                className="mt-1 p-2 w-full border rounded-md text-gray-950"
                defaultValue={data.user.email}
                disabled
              />
            </div>

            {/* Second row */}
            <div className="mb-4 col-span-1">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor="firstName"
              >
                First Name
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                className="mt-1 p-2 w-full border rounded-md text-gray-950"
                defaultValue={data.user.firstName}
              />
            </div>

            <div className="mb-4 col-span-1">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor="lastName"
              >
                Last Name
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                className="mt-1 p-2 w-full border rounded-md text-gray-950"
                defaultValue={data.user.lastName}
              />
            </div>

            {/* Third row */}
            <div className="mb-4 col-span-2">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor="bio"
              >
                Bio
              </label>
              <textarea
                id="bio"
                name="bio"
                rows={3}
                className="mt-1 p-2 w-full border rounded-md text-gray-950"
                defaultValue={data.user.bio}
              ></textarea>
            </div>

            <div>
              <div className="my-6 flex items-center justify-center">
                <ImageField
                  name="image"
                  onFileChange={(selected) => setFile(selected)}
                  previewImage={previewImage}
                  onPreviewImageChange={setPreviewImage}
                />
                <input
                  type="text"
                  name="imageId"
                  hidden
                  defaultValue={data.user.image?.id || undefined}
                />
              </div>
            </div>

            <div className="col-span-2">
              <button
                type="submit"
                aria-disabled={false}
                className="w-full px-8 py-3 text-lg font-semibold rounded bg-violet-400 text-gray-900"
              >
                {isLoading ? "Loading..." : "Update Profile"}
              </button>
            </div>

            {data && (
              <div className="col-span-2">
                <p aria-live="polite" className="sr-only" role="status">
                  {message}
                </p>
                <p className="flex justify-center items-center text-purple-700 p-2">
                  {message}
                </p>
              </div>
            )}
          </fieldset>
        </Form>
      </div>
    </div>
  );
}
</file>

<file path="frontend/app/components/VideoEmbed.tsx">
// components/VideoEmbed.tsx

import React from "react";

interface VideoEmbedProps {
  id: number;
  url: string;
  width?: string;
  height?: string;
}

const getEmbedUrl = (videoUrl: string): string | null => {
  const youtubeRegex =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|watch\?v%3D)([\w-]{11}).*/;
  const youtubeMatch = videoUrl.match(youtubeRegex);

  if (youtubeMatch && youtubeMatch[2].length === 11) {
    return `https://www.youtube.com/embed/${youtubeMatch[2]}`;
  }

  // Add support for other video platforms here

  return null;
};

export default function VideoEmbed({ data }: { data: VideoEmbedProps }) {
  const embedUrl = getEmbedUrl(data.url);

  if (!embedUrl) return <div>Invalid video URL</div>;

  return (
    <div className="video-embed relative pb-56.25 h-72 lg:h-[450px] overflow-hidden my-8">
      <iframe
        title="video"
        src={embedUrl || ""}
        width={data.width || "100%"}
        height={data.height || "100%"}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="absolute top-0 left-0 w-full h-full"
      />
    </div>
  );
}
</file>

<file path="frontend/app/lib/utils.ts">
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
</file>

<file path="frontend/app/routes/_index.tsx">
import type { MetaFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { sectionRenderer } from "~/utils/section-renderer";

export const meta: MetaFunction = () => {
  return [{ title: "New Remix App" }];
};

export async function loader() {
  const path = `/pages`;
  const slug = "home";
  const urlParamsObject = { filters: { slug } };
  const response = await fetchStrapiData(path, urlParamsObject);
  return response;
}

export default function RootRoute() {
  const page = useLoaderData<typeof loader>();
  if (page.data.length === 0) return <div className="container mx-auto p-8 text-white">Please publish your first page from Strapi Admin</div>;
  const contentSections = page.data[0].attributes.contentSections;
  return contentSections.map((section: any, index: number) =>
    sectionRenderer(section, index)
  );
}
</file>

<file path="frontend/app/routes/api.join-form.tsx">
import { json } from "@remix-run/node";
import { useFetcher } from "@remix-run/react";
import { submitJoinForm } from "~/api/form.server";
import type { FC } from "react";

export async function action({ request }: { request: Request }) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const formData = await request.formData();
  const email = formData.get("email");

  if (!email) return json({ error: "Missing email" });

  if (!emailRegex.test(formData.get("email") as string))
    return json({ error: "Invalid email" });

  const formBody = { data: { email: formData.get("email") } };

  const response = await submitJoinForm(
    formBody as { data: { email: string } }
  );

  if (response.status !== 200) return json({ error: response.statusText });
  const data = await response.json();
  return json({ data: data });
}

// TODO: TYPE STRAPI FORM RESPONSE AND PASS TO USE FETCHER
// NOTE: PASS TYPES VIA GENERICS WHEN USING REACT COMPONENTS
const FormSubmit: FC<{
  placeholder: string;
  text: string;
}> = ({ placeholder, text }) => {
  const fetcher = useFetcher();

  if (fetcher.data && !fetcher.data.error) {
    return (
      <div className="text-center mt-8">
        <h1 className="text-3xl font-bold mb-4 text-pink-500">Thank You!</h1>
        <p className="text-gray-500 text-lg">
          We appreciate your interest in our community. We'll be in touch soon!
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-row items-center self-center justify-center flex-shrink-0 shadow-md lg:justify-end">
      <div className="flex flex-col">
        <div className="flex flex-row">
          <fetcher.Form method="post" action="/api/join-form">
            <input
              name={"email"}
              type="text"
              placeholder={placeholder}
              className={"w-3/5 p-3 rounded-l-lg sm:w-2/3 text-gray-700"}
            />
            <button
              type="submit"
              className="w-2/5 p-3 font-semibold rounded-r-lg sm:w-1/3 bg-violet-400 text-gray-900"
            >
              {text}
            </button>
            {fetcher.data?.error && (
              <p className="text-red-500 bg-red-200 px-4 py-2 rounded-lg my-2">
                {fetcher.data.error}
              </p>
            )}
          </fetcher.Form>
        </div>
      </div>
    </div>
  );
};


export default FormSubmit;
</file>

<file path="frontend/app/routes/blog.$category.tsx">
import { Outlet } from '@remix-run/react';
export default function CategoryRoute() {
  return <Outlet />;
}
</file>

<file path="frontend/app/routes/login.tsx">
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { StrapiErrorResponse } from "~/types";

import { json, redirect } from "@remix-run/node";
import { useActionData, useLoaderData } from "@remix-run/react";
import { LoginForm } from "~/components/LoginForm";
import { login } from "~/api/auth/login.server";
import { getUserData } from "~/utils/session.server";

export async function loader({ request } : LoaderFunctionArgs) {
  const user = await getUserData(request);
  if (user) return redirect("/blog");
  return null;
} 

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const data = {
    identifier: formData.get("identifier"),
    password: formData.get("password"),
  };

  const errors = {
    username: data.identifier ? null : "Username/Email is required",
    password: data.password ? null : "Password is required",
  };

  const hasErrors = Object.values(errors).some((errorMessage) => errorMessage);

  if (hasErrors) return json({ message: "Please complete all the fields."});
  const response = await login("/blog", data) as StrapiErrorResponse;
  if (response.error) return json({ message: response.error.message });
  return response;
}

const RegisterRoute: React.FC = () => {
  const actionData = useActionData<typeof action>();
  const loaderData = useLoaderData<typeof loader>();
  return <LoginForm data={ loaderData || actionData} />;
};

export default RegisterRoute;
</file>

<file path="frontend/app/routes/logout.tsx">
import type { ActionFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { Form } from "@remix-run/react";

import { logout } from "~/utils/session.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  return logout(request);
};

export const loader = async () => {
  return redirect("/");
};

export function Logout() {
  return (
    <div className="hidden md:flex md:items-center md:space-x-6">
      <Form method="post" action="/logout">
        <button type="submit">
          <span className="cursor-pointer hover:text-fuchsia-400">Logout</span> &nbsp;
          <span aria-hidden="true">&rarr;</span>
        </button>
      </Form>
    </div>
  );
}
</file>

<file path="frontend/app/routes/profile.tsx">
import {
  type LoaderFunctionArgs,
  type ActionFunctionArgs,
  redirect,
  json,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { userme } from "~/api/auth/userme.server";
import PageHeader from "~/components/PageHeader";
import UserProfileForm from "~/components/UserProfileForm";
import { getUserData } from "~/utils/session.server";
import { updateProfile } from "~/api/update-user-profile.server";
import { uploadImage } from "~/api/upload-image.server";

import { deleteImage as deleteOldImage } from "~/api/delete-image.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await userme(request);
  if (!user) return redirect("/login");
  return json({ user });
}

export async function action({ request }: ActionFunctionArgs) {
  const user = await getUserData(request);
  let formData;

  const isMultipart = request.headers
    .get("Content-Type")
    ?.includes("multipart");

  const uploadHandler = unstable_createMemoryUploadHandler({
    maxPartSize: 500_000_000,
  });

  if (isMultipart) {
    formData = await unstable_parseMultipartFormData(request, uploadHandler);
  } else {
    formData = await request.formData();
  }

  const formItems = Object.fromEntries(formData);
  const { image, imageId, ...items } = formItems;

  const imageResponse = await uploadImage(image, user.jwt);
  const newImageId = imageResponse[0]?.id || null;

  const updateResponse = await updateProfile(
    items,
    newImageId,
    user.user.id,
    user.jwt
  );

  if (newImageId) await deleteOldImage(imageId as string, user.jwt);

  console.dir(updateResponse, "####################################");
  return json({ message: "Profile updated" });
}

export default function ProfileRoute() {

  const loaderData = useLoaderData<typeof loader>();

  return (
    <div>
      <PageHeader heading="Profile" text="User Details" />
      <UserProfileForm data={loaderData as any} />
    </div>
  );
}
</file>

<file path="frontend/app/routes/register.tsx">
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { StrapiErrorResponse } from "~/types";

import { json, redirect } from "@remix-run/node";
import { useActionData, useLoaderData } from "@remix-run/react";
import { RegisterForm } from "~/components/RegisterForm";
import { register } from "~/api/auth/register.server";
import { getUserData } from "~/utils/session.server";

export async function loader({ request } : LoaderFunctionArgs) {
  const user = await getUserData(request);
  if (user) return redirect("/blog");
  return null;
} 

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();

  const data = {
    username: formData.get("username"),
    email: formData.get("email"),
    password: formData.get("password"),
  };

  const errors = {
    username: data.username ? null : "Username is required",
    email: data.email ? null : "Email is required",
    password: data.password ? null : "Password is required",
  };

  const hasErrors = Object.values(errors).some((errorMessage) => errorMessage);

  if (hasErrors) return json({ message: "Please complete all the fields."});
  const response = await register("/blog", data) as StrapiErrorResponse;

  if (response.error) return json({ message: response.error.message });
  return response;
}

const RegisterRoute: React.FC = () => {
  const actionData = useActionData<typeof action>();
  const loaderData = useLoaderData<typeof loader>();
  return <RegisterForm data={ loaderData || actionData} />;
};

export default RegisterRoute;
</file>

<file path="frontend/app/utils/api-helpers.ts">
interface Headers {
  [key: string]: string;
}

export function getStrapiURL(path = "") {
  return ENV.STRAPI_API_URL + path;
}

export function createHeaders(jwt: string | null): Headers {
  const headers: Headers = { "Content-Type": "application/json" };
  if (jwt) headers["Authorization"] = `Bearer ${jwt}`;
  return headers;
}

export function getStrapiMedia(url: string | null) {
  if (url == null) {
    return null;
  }

  // Return the full URL if the media is hosted on an external provider
  if (url.startsWith("http") || url.startsWith("//")) {
    return url;
  }

  // Otherwise prepend the URL path with the Strapi URL
  return `${getStrapiURL()}${url}`;
}

export function formatDate(dateString: string) {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
  };
  return date.toLocaleDateString("en-US", options);
}
</file>

<file path="frontend/app/utils/post-renderer.tsx">
import RichText from "~/components/RichText";
import ImageSlider from "~/components/ImageSlider";
import Quote from "~/components/Quote";
import Media from "~/components/Media";
import VideoEmbed from "~/components/VideoEmbed";

export function postRenderer(section: any, index: number) {
  switch (section.__component) {
    case "shared.rich-text":
      return <RichText key={index} data={section} />;
    case "shared.slider":
      return <ImageSlider key={index} data={section} />;
    case "shared.quote": 
      return <Quote key={index} data={section} />;
    case "shared.media":
      return <Media key={index} data={section} />;
    case "shared.video-embed":
      return <VideoEmbed key={index} data={section} />;
    default:
      return null;
  }
}
</file>

<file path="frontend/app/utils/render-button-style.ts">
export function renderButtonStyle(type: string) {
	switch (type) {
		case "primary":
			return "px-8 py-3 text-lg font-semibold rounded bg-violet-400 text-gray-900";
		case "secondary":
			return "px-8 py-3 text-lg font-semibold border rounded border-gray-100";
		default:
			return "px-8 py-3 text-lg font-semibold rounded bg-violet-400 text-gray-900";
	}
}
</file>

<file path="frontend/app/utils/section-renderer.tsx">
import Hero from "../components/Hero";
import GradientHero from "../components/GradientHero";
import Features from "../components/Features";
import Testimonials from "../components/Testimonials";
import Pricing from "../components/Pricing";
import Email from "../components/Email";
import Overview from "../components/Overview";
import Separator from "../components/Separator";
import ClientLogo from "../components/ClientLogo";
import LargeImage from "~/components/LargeImage";
export function sectionRenderer(section: any, index: number) {


  switch (section.__component) {
    case "sections.hero":
      // return <Hero key={index} data={section} />;
      return <GradientHero key={index} data={section} />;
    case "sections.features":
      return <Features key={index} data={section} />;
    case "sections.testimonials-group":
      return <Testimonials key={index} data={section} />;
    case "sections.pricing":
      return <Pricing key={index} data={section} />;
    case "sections.lead-form":
      return <Email key={index} data={section} />;
    case "sections.overview":
      return <Overview key={index} data={section} />;
    case "sections.separator":
      return <Separator key={index} data={section} />;
    case "sections.client-logo":
      return <ClientLogo key={index} data={section} />;
    case "sections.large-image":
      console.log("Media section", section);
      return <LargeImage key={index} data={section} />;
      
      // return <img key={index} src={section.url} alt={section.alt || "Media"} />;
    default:
      return null;
  }
}
</file>

<file path="frontend/app/utils/session.server.ts">
import { createCookieSessionStorage, redirect } from '@remix-run/node';

const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) throw new Error('Please set the SESSION_SECRET environment variable');

const { getSession, commitSession, destroySession } = createCookieSessionStorage({
  cookie: {
    name: 'user-session',
    secrets: [sessionSecret],
    path: '/',
    httpOnly: true,
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 1 week
  },
});

export async function createUserSession(redirectTo: string, user: any) {
  const sessionData = await getSession();
  sessionData.set('user-session', user);
  return redirect(redirectTo, {
    headers: { 'Set-Cookie': await commitSession(sessionData) }
  });
}

function getUserSession(request: Request) {
  return getSession(request.headers.get('Cookie'));
}

export async function getUserData(request: Request) {
  const session = await getUserSession(request);
  return session.get('user-session');
}

export async function logout(request: Request) {
  const sessionData = await getUserSession(request);
  return redirect("/", {
    headers: { 'Set-Cookie': await destroySession(sessionData) }
  })
}
</file>

<file path="frontend/app/entry.client.tsx">
/**
 * By default, Remix will handle hydrating your app on the client for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.client
 */

import { RemixBrowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";

startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <RemixBrowser />
    </StrictMode>
  );
});
</file>

<file path="frontend/app/entry.server.tsx">
/**
 * By default, Remix will handle generating the HTTP Response for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.server
 */

import { PassThrough } from "node:stream";

import type { AppLoadContext, EntryContext } from "@remix-run/node";
import { createReadableStreamFromReadable } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import isbot from "isbot";
import { renderToPipeableStream } from "react-dom/server";

const ABORT_DELAY = 5_000;

global.ENV = { STRAPI_API_URL: process.env.STRAPI_API_URL };

export default function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  loadContext: AppLoadContext
) {
  return isbot(request.headers.get("user-agent"))
    ? handleBotRequest(
        request,
        responseStatusCode,
        responseHeaders,
        remixContext
      )
    : handleBrowserRequest(
        request,
        responseStatusCode,
        responseHeaders,
        remixContext
      );
}

function handleBotRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext
) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const { pipe, abort } = renderToPipeableStream(
      <RemixServer
        context={remixContext}
        url={request.url}
        abortDelay={ABORT_DELAY}
      />,
      {
        onAllReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            })
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          // Log streaming rendering errors from inside the shell.  Don't log
          // errors encountered during initial shell rendering since they'll
          // reject and get logged in handleDocumentRequest.
          if (shellRendered) {
            console.error(error);
          }
        },
      }
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

function handleBrowserRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext
) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const { pipe, abort } = renderToPipeableStream(
      <RemixServer
        context={remixContext}
        url={request.url}
        abortDelay={ABORT_DELAY}
      />,
      {
        onShellReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            })
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          // Log streaming rendering errors from inside the shell.  Don't log
          // errors encountered during initial shell rendering since they'll
          // reject and get logged in handleDocumentRequest.
          if (shellRendered) {
            console.error(error);
          }
        },
      }
    );

    setTimeout(abort, ABORT_DELAY);
  });
}
</file>

<file path="frontend/app/env.server.ts">
export function getEnv() {
  return {
    STRAPI_API_URL: process.env.STRAPI_API_URL,
  }
}
</file>

<file path="frontend/app/root.tsx">
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import stylesheet from "~/tailwind.css";
import { json } from "@remix-run/node";
import { getStrapiMedia } from "./utils/api-helpers";
import { fetchStrapiData } from "~/api/fetch-strapi-data.server";
import { userme } from "./api/auth/userme.server";

import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  isRouteErrorResponse,
  useRouteError,
  useLoaderData,
} from "@remix-run/react";

import Navbar from "~/components/Navbar";
import Footer from "~/components/Footer";
import Banner from "~/components/Banner";
export const links: LinksFunction = () => [
  { rel: "stylesheet", href: stylesheet },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const path = `/global`;

  const urlParamsObject = {
    populate: [
      "metadata.shareImage",
      "favicon",
      "notificationBanner.link",
      "navbar.links",
      "navbar.navbarLogo.logoImg",
      "footer.footerLogo.logoImg",
      "footer.menuLinks",
      "footer.legalLinks",
      "footer.socialLinks",
      "footer.categories",
    ],
  };

  const response = await fetchStrapiData(path, urlParamsObject);

  const user = await userme(request);

  return json({
    ...response,
    ENV: (global as any).ENV,
    user: user || null,
  });
}

export function ErrorBoundary() {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div>
        <h1>
          {error.status} {error.statusText}
        </h1>
        <p>{error.data}</p>
      </div>
    );
  } else if (error instanceof Error) {
    return (
      <div>
        <h1>Error</h1>
        <p>{error.message}</p>
        <p>The stack trace is:</p>
        <pre>{error.stack}</pre>
      </div>
    );
  } else {
    return <h1>Unknown Error</h1>;
  }
}

export default function App() {
  const data = useLoaderData<typeof loader>();
  const { notificationBanner, navbar, footer } = data.data.attributes;
  const navbarLogoUrl = getStrapiMedia(
    navbar.navbarLogo.logoImg.data.attributes.url
  );
  const footerLogoUrl = getStrapiMedia(
    footer.footerLogo.logoImg.data.attributes.url
  );

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
      </head>

      <body className="bg-white min-h-screen flex flex-col">
        <Navbar
          links={navbar.links}
          logoUrl={navbarLogoUrl}
          logoText={navbar.navbarLogo.logoText}
          user={data.user}
        />
        <main className="pt-16 flex-grow">
          <Outlet />
        </main>

        <Footer
          className=" bottom-0 w-full py-6 text-center"
          logoUrl={footerLogoUrl || ""}
          logoText={footer.footerLogo.logoText}
          menuLinks={footer.menuLinks}
          categoryLinks={footer.categories.data}
          legalLinks={footer.legalLinks}
          socialLinks={footer.socialLinks}
        />
        {/* <Banner data={notificationBanner} /> */}
        <script
          dangerouslySetInnerHTML={{
            __html: `window.ENV = ${JSON.stringify(data.ENV)}`,
          }}
        />

        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
</file>

<file path="frontend/app/tailwind.css">
@tailwind base;
@tailwind components;
@tailwind utilities;


/******************************************* 
  Rich Text Styles
*******************************************/ 

/* Headers */
.rich-text article  h1 {
  @apply text-4xl font-bold mb-4 text-eguard;
}

.rich-text article  h2 {
  @apply text-3xl font-bold mb-4 text-eguard;
}

.rich-text article  h3 {
  @apply text-2xl font-bold mb-4 text-eguard;
}

.rich-text article  h4 {
  @apply text-xl font-bold mb-4 text-eguard;
}

.rich-text article  h5 {
  @apply text-lg font-bold mb-4 text-eguard;
}

.rich-text article  h6 {
  @apply text-base font-bold mb-4 text-violet-200;
}

/* Horizontal rules */
.rich-text article  hr {
  @apply border-gray-300 my-8;
}

.rich-text article  a {
  @apply text-eguard underline;
}

/* Typographic replacements */
.rich-text article  p {
  @apply mb-4;
}

/* Emphasis */
.rich-text article  strong {
  @apply font-bold;
}

.rich-text article  em {
  @apply italic;
}

.rich-text article  del {
  @apply line-through;
}

/* Blockquotes */
.rich-text article  blockquote {
  @apply border-l-4 border-gray-400 pl-4 py-2 mb-4;
}

/* Lists */
.rich-text article  ul {
  @apply list-disc pl-4 mb-4;
}

.rich-text article  ol {
  @apply list-decimal pl-4 mb-4;
}

.rich-text article  li {
  @apply mb-2;
}

.rich-text article  li > ul {
  @apply list-disc pl-4 mb-2;
}

.rich-text article  li > ol {
  @apply list-decimal pl-4 mb-2;
}

/* Code blocks */
.rich-text article  pre {
  @apply font-mono bg-gray-900 text-gray-100 rounded p-4  my-6;
}

.rich-text article  code {
  @apply font-mono bg-gray-900 text-gray-100 rounded px-2 py-1;
}

/* Tables */
.rich-text article  table {
  @apply w-full border-collapse border-gray-300 my-6;
}

.rich-text article  th {
  @apply bg-gray-900 text-left py-2 px-4 font-semibold border-b border-gray-300;
}

.rich-text article  td {
  @apply py-2 px-4 border-b border-gray-300;
}

/* Images */
.rich-text article  img {
  @apply w-full object-cover rounded-xl my-6;
}

/* Custom containers */
.rich-text article  .warning {
  @apply bg-yellow-100 border-yellow-500 text-yellow-700 px-4 py-2 rounded-lg mb-4;
}


/******************************************* 
  React Slideshow Styles
*******************************************/   

.react-slideshow-container [aria-label="Previous Slide"] {
  margin-left: 1rem;
}

.react-slideshow-container [aria-label="Next Slide"] {
  margin-right: 1rem;
}

/*---break---*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/*---break---*/

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
</file>

<file path="frontend/app/types.ts">
export interface StrapiErrorResponse {
  error?: {
    statusCode: number;
    error: string;
    message: string;
  };
}

export interface StrapiUserResponse {
  id: number;
  username: string;
  bio: string;
  image: {
    id: string;
    url: string;
    alternativeText: string;
  };
}
</file>

<file path="frontend/public/nav.svg">
<svg width="184" height="53" viewBox="0 0 184 53" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M148.511 0H35.4894C28.8619 0 23.4894 5.37135 23.4894 11.9988V33.9744C23.4894 50.735 20.693 53 0 53H184C163.307 53 160.511 50.735 160.511 33.9744V11.9988C160.511 5.37135 155.138 0 148.511 0Z" fill="#F1F8FF"/>
</svg>
</file>

<file path="frontend/.env.example">
SUBMIT_FORM_STRAPI_KEY=to_be_replaced
STRAPI_API_URL=http://localhost:1337
SESSION_SECRET=secret
</file>

<file path="frontend/.eslintrc.cjs">
/** @type {import('eslint').Linter.Config} */
module.exports = {
  extends: ["@remix-run/eslint-config", "@remix-run/eslint-config/node"],
};
</file>

<file path="frontend/.gitignore">
node_modules

/.cache
/build
/public/build
.env
</file>

<file path="frontend/components.json">
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "app/tailwind.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "~/components",
    "utils": "~/lib/utils",
    "ui": "~/components/ui",
    "lib": "~/lib",
    "hooks": "~/hooks"
  },
  "iconLibrary": "lucide"
}
</file>

<file path="frontend/package.json">
{
  "name": "frontend",
  "private": true,
  "sideEffects": false,
  "type": "module",
  "scripts": {
    "build": "remix build",
    "dev": "remix dev --manual",
    "start": "remix-serve ./build/index.js",
    "typecheck": "tsc"
  },
  "dependencies": {
    "@markdoc/markdoc": "^0.4.0",
    "@remix-run/css-bundle": "^2.3.1",
    "@remix-run/node": "^2.3.1",
    "@remix-run/react": "^2.3.1",
    "@remix-run/serve": "^2.3.1",
    "autoprefixer": "^10.4.21",
    "class-variance-authority": "^0.7.1",
    "classnames": "^2.3.2",
    "clsx": "^2.1.1",
    "isbot": "^3.7.1",
    "lucide-react": "^0.482.0",
    "qs": "^6.11.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-icons": "^4.12.0",
    "react-slideshow-image": "^4.3.0",
    "tailwind-merge": "^3.0.2",
    "tailwindcss-animate": "^1.0.7"
  },
  "devDependencies": {
    "@remix-run/dev": "^2.3.1",
    "@remix-run/eslint-config": "^2.3.1",
    "@types/qs": "^6.9.10",
    "@types/react": "^18.2.42",
    "@types/react-dom": "^18.2.17",
    "eslint": "^8.55.0",
    "tailwindcss": "^3.3.6",
    "typescript": "^5.3.3"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
</file>

<file path="frontend/postcss.config.js">
export default {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  }
</file>

<file path="frontend/README.md">
# Welcome to Remix!

- [Remix Docs](https://remix.run/docs)

## Development

From your terminal:

```sh
npm run dev
```

This starts your app in development mode, rebuilding assets on file changes.

## Deployment

First, build your app for production:

```sh
npm run build
```

Then run the app in production mode:

```sh
npm start
```

Now you'll need to pick a host to deploy it to.

### DIY

If you're familiar with deploying node applications, the built-in Remix app server is production-ready.

Make sure to deploy the output of `remix build`

- `build/`
- `public/build/`
</file>

<file path="frontend/remix.config.js">
/** @type {import('@remix-run/dev').AppConfig} */
export default {
  ignoredRouteFiles: ["**/.*"],
  // appDirectory: "app",
  // assetsBuildDirectory: "public/build",
  // publicPath: "/build/",
  // serverBuildPath: "build/index.js",
  tailwind: true,
  postcss: true,
};
</file>

<file path="frontend/remix.env.d.ts">
/// <reference types="@remix-run/dev" />
/// <reference types="@remix-run/node" />
</file>

<file path="frontend/tailwind.config.ts">
import type { Config } from 'tailwindcss'

export default {
    darkMode: ['class'],
    content: ['./app/**/*.{js,jsx,ts,tsx}'],
  theme: {
  	extend: {
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		colors: {
			eguard: 'hsl(213, 96%, 54%)',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config
</file>

<file path="frontend/tsconfig.json">
{
  "include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx"],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"]
    },

    // Remix takes care of building everything in `remix build`.
    "noEmit": true
  }
}
</file>

<file path=".gitignore">
node_modules

/.cache
/build
/public/build
.env
.DS_Store
</file>

<file path="copy-env.js">
const fs = require("fs");
const path = require("path");

function copyEnvFile(targetDir) {
  // Ensure targetDir is a valid string and trim any whitespace
  targetDir = typeof targetDir === "string" ? targetDir.trim() : "";

  const examplePath = path.join(targetDir, ".env.example");
  const envPath = path.join(targetDir, ".env");

  console.log("Attempting to copy from:", examplePath);
  console.log("To:", envPath);

  // Check if .env.example exists
  fs.access(examplePath, fs.constants.F_OK, (err) => {
    if (err) {
      console.error(`.env.example file does not exist in ${targetDir}`);
      return;
    }

    // .env.example exists, now check for .env
    fs.access(envPath, fs.constants.F_OK, (err) => {
      if (err) {
        // .env file does not exist, copy .env.example to .env
        fs.copyFile(examplePath, envPath, (err) => {
          if (err) {
            console.error("Error occurred:", err);
            return;
          }
          console.log(`.env.example has been copied to ${envPath}`);
        });
      } else {
        // .env file exists, no action needed
        console.log(
          `.env file already exists in ${targetDir}, no action taken.`
        );
      }
    });
  });
}

// Get the directory path from the command line argument and trim whitespace
const directoryPath = process.argv[2]?.trim();

if (directoryPath) {
  copyEnvFile(directoryPath);
} else {
  console.error("Please provide a directory path as an argument.");
}
</file>

<file path="package.json">
{
  "name": "eguard-web",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "scripts": {
    "frontend": "yarn dev --prefix ../frontend/",
    "backend": "yarn dev --prefix ../backend/",
    "setup:frontend": "cd frontend && yarn && node ../copy-env.js ./",
    "setup:backend": "cd backend && yarn && node ../copy-env.js ./",
    "setup": "yarn install && yarn setup:frontend && yarn setup:backend",
    "dev": "yarn concurrently \"cd backend && yarn develop\" \"cd frontend && yarn dev\"",
    "repo:upstream": "git fetch upstream && git merge upstream/main",
    "seed": "cd backend && yarn strapi import -f ../seed-data.tar.gz",
    "export": "cd backend && yarn strapi export --no-encrypt -f  ../seed-data"
  },
  "dependencies": {
    "concurrently": "^7.6.0"
  },
  "author": "",
  "license": "ISC"
}
</file>

</files>
